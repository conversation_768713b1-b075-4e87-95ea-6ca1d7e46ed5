/* eslint-disable */
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
};

export type AdminLoginResponse = {
  __typename?: 'AdminLoginResponse';
  exp: Scalars['DateTime']['output'];
  token: Scalars['String']['output'];
  user: User;
};

export type AppPaginator = {
  __typename?: 'AppPaginator';
  current_page: Scalars['Int']['output'];
  has_more_pages: Scalars['Boolean']['output'];
  last_page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type BasicStatsResponse = {
  __typename?: 'BasicStatsResponse';
  total_fee_paid?: Maybe<Scalars['Float']['output']>;
  total_fee_unpaid?: Maybe<Scalars['Float']['output']>;
  total_subscriber?: Maybe<Scalars['Int']['output']>;
  total_subscription_fee?: Maybe<Scalars['Float']['output']>;
};

export enum CollectionDay {
  Friday = 'FRIDAY',
  Monday = 'MONDAY',
  Saturday = 'SATURDAY',
  Sunday = 'SUNDAY',
  Thursday = 'THURSDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY'
}

export type CollectionScheduleNotification = {
  __typename?: 'CollectionScheduleNotification';
  establishment: Establishment;
  establishment_id: Scalars['ID']['output'];
  group_id: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  latestWhatsApp?: Maybe<WhatsAppReceipt>;
  sent_at?: Maybe<Scalars['DateTime']['output']>;
  week_day: Scalars['String']['output'];
};

export type CollectionScheduleNotificationStats = {
  __typename?: 'CollectionScheduleNotificationStats';
  date: Scalars['DateTime']['output'];
  group_id: Scalars['String']['output'];
  total: Scalars['Int']['output'];
  week_day: Scalars['String']['output'];
};

export type CollectionScheduleNotificationStatsPaginator = {
  __typename?: 'CollectionScheduleNotificationStatsPaginator';
  data?: Maybe<Array<CollectionScheduleNotificationStats>>;
  paginator_info: AppPaginator;
};

export type CollectionSchedulePaginator = {
  __typename?: 'CollectionSchedulePaginator';
  data?: Maybe<Array<Establishment>>;
  paginator_info: AppPaginator;
};

export type CollectionSentNotificationPaginator = {
  __typename?: 'CollectionSentNotificationPaginator';
  data?: Maybe<Array<CollectionScheduleNotification>>;
  paginator_info: AppPaginator;
};

export type Customer = {
  __typename?: 'Customer';
  address: Scalars['String']['output'];
  alt_phone_number?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  establishments?: Maybe<Array<Establishment>>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  phone_number: Scalars['String']['output'];
  remarks?: Maybe<Scalars['String']['output']>;
  subscriptionHistory?: Maybe<Array<SubscriptionBill>>;
  /** is officially a customer when verified */
  verified_at?: Maybe<Scalars['DateTime']['output']>;
};

export type CustomerListPaginator = {
  __typename?: 'CustomerListPaginator';
  data?: Maybe<Array<Customer>>;
  paginator_info: AppPaginator;
};

export type CustomerSubscriptionHistoryPaginator = {
  __typename?: 'CustomerSubscriptionHistoryPaginator';
  data?: Maybe<Array<SubscriptionBill>>;
  paginator_info: AppPaginator;
};

export enum CustomerType {
  Approved = 'APPROVED',
  PendingApproval = 'PENDING_APPROVAL'
}

export type Establishment = {
  __typename?: 'Establishment';
  address: Scalars['String']['output'];
  alt_phone_number?: Maybe<Scalars['String']['output']>;
  collection_days?: Maybe<Array<Scalars['String']['output']>>;
  current_plan_id: Scalars['ID']['output'];
  current_plan_period: Scalars['Int']['output'];
  customer: Customer;
  id: Scalars['ID']['output'];
  latestWhatsApp?: Maybe<WhatsAppReceipt>;
  name: Scalars['String']['output'];
  phone_number: Scalars['String']['output'];
  plan: Plan;
  remarks?: Maybe<Scalars['String']['output']>;
  subscription_status: Scalars['String']['output'];
  zone?: Maybe<Zone>;
  zone_id: Scalars['ID']['output'];
};

export type EstablishmentInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  alt_phone_number?: InputMaybe<Scalars['String']['input']>;
  collection_days?: InputMaybe<Array<Scalars['String']['input']>>;
  current_plan_id?: InputMaybe<Scalars['ID']['input']>;
  /** for updating existing establishment */
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};

export type GetPaymentRecordPaginator = {
  __typename?: 'GetPaymentRecordPaginator';
  data?: Maybe<Array<SubscriptionBill>>;
  paginator_info: AppPaginator;
};

export type GetSalesPaginator = {
  __typename?: 'GetSalesPaginator';
  data?: Maybe<Array<Sale>>;
  paginator_info: AppPaginator;
};

export type Mutation = {
  __typename?: 'Mutation';
  /** customer are automatically verified */
  addCustomer: Scalars['Boolean']['output'];
  addPlan: Scalars['Boolean']['output'];
  addSale: Scalars['Boolean']['output'];
  /** for offline susbcription */
  addSubscription: Scalars['Boolean']['output'];
  adminLogin: AdminLoginResponse;
  deleteCustomer: Scalars['Boolean']['output'];
  deleteEstablishment?: Maybe<Scalars['Boolean']['output']>;
  deletePlan: Scalars['Boolean']['output'];
  deleteSale: Sale;
  deleteSubscription: Scalars['Boolean']['output'];
  generatePaymentReceiptLink: Scalars['String']['output'];
  logout: Scalars['Boolean']['output'];
  markAsPaid: Scalars['Boolean']['output'];
  processSubscriptionPaymentLink: PaymentableOrder;
  /** for public user registration */
  registerCustomer: Scalars['Boolean']['output'];
  resendPaymentLink: Scalars['Boolean']['output'];
  /** send notification to customer regarding collection schedule */
  sendCollectionScheduleNotiToEstablishment: Scalars['Boolean']['output'];
  sendCollectionScheduleNotiToStaff: Scalars['Boolean']['output'];
  updateCustomer: Scalars['Boolean']['output'];
  updateEstablishment: Scalars['Boolean']['output'];
  updatePlan: Scalars['Boolean']['output'];
  updateProduct: Scalars['Boolean']['output'];
  updateSubscription: Scalars['Boolean']['output'];
};


export type MutationAddCustomerArgs = {
  address: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
};


export type MutationAddPlanArgs = {
  category: PlanCategory;
  is_internal: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  price: Scalars['Float']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
};


export type MutationAddSaleArgs = {
  customer_id: Scalars['ID']['input'];
  payment_mode: PaymentMode;
  products: Array<ProductInput>;
};


export type MutationAddSubscriptionArgs = {
  customer_id: Scalars['ID']['input'];
  establishment_input: EstablishmentInput;
  subscription_input: SubscriptionInput;
};


export type MutationAdminLoginArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationDeleteCustomerArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteEstablishmentArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeletePlanArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSaleArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSubscriptionArgs = {
  id: Scalars['Int']['input'];
};


export type MutationGeneratePaymentReceiptLinkArgs = {
  id: Scalars['ID']['input'];
  phone_number?: InputMaybe<Scalars['String']['input']>;
  should_send: Scalars['Boolean']['input'];
};


export type MutationMarkAsPaidArgs = {
  payment_date?: InputMaybe<Scalars['DateTime']['input']>;
  payment_mode: PaymentMode;
  remarks?: InputMaybe<Scalars['String']['input']>;
  subscription_id: Scalars['ID']['input'];
};


export type MutationProcessSubscriptionPaymentLinkArgs = {
  payment_link_hash: Scalars['String']['input'];
};


export type MutationRegisterCustomerArgs = {
  address: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  plan_id: Scalars['ID']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
};


export type MutationResendPaymentLinkArgs = {
  customer_id?: InputMaybe<Scalars['ID']['input']>;
  payable_amount?: InputMaybe<Scalars['Float']['input']>;
  phone_number: Scalars['String']['input'];
  subscription_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationSendCollectionScheduleNotiToEstablishmentArgs = {
  establishment_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  week_day: Scalars['String']['input'];
};


export type MutationSendCollectionScheduleNotiToStaffArgs = {
  sent_at: Scalars['DateTime']['input'];
  staff_phone_number: Scalars['String']['input'];
};


export type MutationUpdateCustomerArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateEstablishmentArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  alt_phone_number?: InputMaybe<Scalars['String']['input']>;
  collection_days?: InputMaybe<Array<CollectionDay>>;
  current_plan_id?: InputMaybe<Scalars['ID']['input']>;
  current_plan_period?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationUpdatePlanArgs = {
  category?: InputMaybe<PlanCategory>;
  id: Scalars['ID']['input'];
  is_internal?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateProductArgs = {
  discount?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['ID']['input'];
  item_name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  stock?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateSubscriptionArgs = {
  id: Scalars['ID']['input'];
  paid_at?: InputMaybe<Scalars['DateTime']['input']>;
  payable_amount?: InputMaybe<Scalars['Float']['input']>;
  payment_mode?: InputMaybe<PaymentMode>;
  payment_remarks?: InputMaybe<Scalars['String']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export enum PaymentMode {
  Card = 'CARD',
  Cash = 'CASH',
  Cheque = 'CHEQUE',
  Paymentlink = 'PAYMENTLINK',
  Upi = 'UPI'
}

export type PaymentOrder = {
  __typename?: 'PaymentOrder';
  id: Scalars['ID']['output'];
  paymentable: PaymentableOrder;
  paymentable_id: Scalars['ID']['output'];
  paymentable_type: Scalars['String']['output'];
};

export type PaymentableOrder = RzpayPaymentOrder;

export type Plan = {
  __typename?: 'Plan';
  category: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  is_internal: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  remarks?: Maybe<Scalars['String']['output']>;
};

export enum PlanCategory {
  Commercial = 'COMMERCIAL',
  Domestic = 'DOMESTIC'
}

export type Product = {
  __typename?: 'Product';
  discount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  item_name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  stock: Scalars['Int']['output'];
};

export type ProductInput = {
  product_id: Scalars['ID']['input'];
  quantity: Scalars['Int']['input'];
};

export type ProductSale = {
  __typename?: 'ProductSale';
  discount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  price_per_unit: Scalars['Float']['output'];
  product: Product;
  product_id: Scalars['ID']['output'];
  quantity: Scalars['Int']['output'];
  sale_id: Scalars['ID']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  checkPaymentStatus: SubscriptionBill;
  customerById?: Maybe<Customer>;
  getBasicStats: BasicStatsResponse;
  /** customer list by collection days */
  getCollectionSchedule: CollectionSchedulePaginator;
  getCollectionScheduleNotificationStats: CollectionScheduleNotificationStatsPaginator;
  getCollectionSentNotificationHistories: CollectionSentNotificationPaginator;
  getCustomerEstablishment?: Maybe<Array<Establishment>>;
  getCustomerSubscriptionHistory: CustomerSubscriptionHistoryPaginator;
  getCustomers: CustomerListPaginator;
  getPaymentReceiptLink?: Maybe<Scalars['String']['output']>;
  getPaymentRecords: GetPaymentRecordPaginator;
  getPlans?: Maybe<Array<Plan>>;
  getProducts?: Maybe<Array<Product>>;
  getSales: GetSalesPaginator;
  getZones?: Maybe<Array<Zone>>;
  searchEstablishment?: Maybe<Array<Establishment>>;
  subscriberGrowthTrend?: Maybe<Array<SubscriberGrowthTrend>>;
  subscriberPlanDistribution?: Maybe<Array<SubscriberPlanDistribution>>;
  subscriptionPaymentTrend?: Maybe<Array<SubscriptionPaymentTrend>>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCheckPaymentStatusArgs = {
  order_id: Scalars['String']['input'];
  payment_id: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCustomerByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetBasicStatsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCollectionScheduleArgs = {
  collection_day?: InputMaybe<CollectionDay>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCollectionScheduleNotificationStatsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCollectionSentNotificationHistoriesArgs = {
  first: Scalars['Int']['input'];
  group_id: Scalars['String']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCustomerEstablishmentArgs = {
  customer_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCustomerSubscriptionHistoryArgs = {
  customer_id: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCustomersArgs = {
  customer_type?: InputMaybe<CustomerType>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPaymentReceiptLinkArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPaymentRecordsArgs = {
  first: Scalars['Int']['input'];
  is_paid?: InputMaybe<Scalars['Boolean']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  month?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSalesArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  product_name?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySearchEstablishmentArgs = {
  keyword: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySubscriberGrowthTrendArgs = {
  year: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySubscriberPlanDistributionArgs = {
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySubscriptionPaymentTrendArgs = {
  month: Scalars['Int']['input'];
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  year: Scalars['Int']['input'];
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};

export type RzpayPaymentOrder = {
  __typename?: 'RzpayPaymentOrder';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_error?: Maybe<Scalars['String']['output']>;
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_instrument_type?: Maybe<Scalars['String']['output']>;
  payment_status: Scalars['String']['output'];
  refund_amount?: Maybe<Scalars['String']['output']>;
  refund_error?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
};

export type Sale = {
  __typename?: 'Sale';
  created_at: Scalars['DateTime']['output'];
  customer: Customer;
  customer_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  payment_mode: Scalars['String']['output'];
  productSales: Array<ProductSale>;
  products: Array<Product>;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type SubscriberGrowthTrend = {
  __typename?: 'SubscriberGrowthTrend';
  month: Scalars['Int']['output'];
  total_subscriber: Scalars['Int']['output'];
};

export type SubscriberPlanDistribution = {
  __typename?: 'SubscriberPlanDistribution';
  plan_name: Scalars['String']['output'];
  total_subscriber: Scalars['Int']['output'];
};

export type SubscriptionBill = {
  __typename?: 'SubscriptionBill';
  customer: Customer;
  customer_id: Scalars['ID']['output'];
  end_date?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  latestWhatsApp?: Maybe<WhatsAppReceipt>;
  overdue: Scalars['Float']['output'];
  paid_at?: Maybe<Scalars['DateTime']['output']>;
  payable_amount: Scalars['Float']['output'];
  paymentOrder?: Maybe<PaymentOrder>;
  payment_link_hash?: Maybe<Scalars['String']['output']>;
  payment_mode?: Maybe<Scalars['String']['output']>;
  payment_receipt_link?: Maybe<Scalars['String']['output']>;
  remarks?: Maybe<Scalars['String']['output']>;
  start_date?: Maybe<Scalars['DateTime']['output']>;
  subscriptionItems?: Maybe<Array<SubscriptionBillItem>>;
  whatsapp_number: Scalars['String']['output'];
  whatsappable?: Maybe<Array<WhatsAppReceipt>>;
};

export type SubscriptionBillItem = {
  __typename?: 'SubscriptionBillItem';
  establishment: Establishment;
  establishment_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  period: Scalars['Int']['output'];
  plan?: Maybe<Plan>;
  plan_id?: Maybe<Scalars['ID']['output']>;
  price: Scalars['Float']['output'];
  subscriptionBill: SubscriptionBill;
  subscription_bill_id: Scalars['ID']['output'];
};

export type SubscriptionInput = {
  /** for updating existing subscription */
  id?: InputMaybe<Scalars['ID']['input']>;
  /** default is 1, i.e 1 month */
  period?: InputMaybe<Scalars['Int']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type SubscriptionPaymentTrend = {
  __typename?: 'SubscriptionPaymentTrend';
  day: Scalars['Int']['output'];
  total_amount: Scalars['Float']['output'];
};

export enum SubscriptionStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who uses this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  role: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
  username: Scalars['String']['output'];
};

export type WhatsAppReceipt = {
  __typename?: 'WhatsAppReceipt';
  created_at: Scalars['DateTime']['output'];
  error_message?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  message_status: Scalars['String']['output'];
  purpose?: Maybe<Scalars['String']['output']>;
  template_name: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  wamid: Scalars['String']['output'];
};

export type Zone = {
  __typename?: 'Zone';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type AdminLoginMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type AdminLoginMutation = { __typename?: 'Mutation', adminLogin: { __typename?: 'AdminLoginResponse', exp: any, token: string, user: { __typename?: 'User', id: string, username: string, role: string } } };

export type DeleteCustomerMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteCustomerMutation = { __typename?: 'Mutation', deleteCustomer: boolean };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type SendCollectionScheduleNotificationMutationVariables = Exact<{
  establishment_ids?: InputMaybe<Array<Scalars['ID']['input']> | Scalars['ID']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  week_day: Scalars['String']['input'];
}>;


export type SendCollectionScheduleNotificationMutation = { __typename?: 'Mutation', sendCollectionScheduleNotiToEstablishment: boolean };

export type UpdateCustomerMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateCustomerMutation = { __typename?: 'Mutation', updateCustomer: boolean };

export type CheckPaymentStatusQueryVariables = Exact<{
  order_id: Scalars['String']['input'];
  payment_id: Scalars['String']['input'];
}>;


export type CheckPaymentStatusQuery = { __typename?: 'Query', checkPaymentStatus: { __typename?: 'SubscriptionBill', id: string, payment_receipt_link?: string | null, payable_amount: number, paid_at?: any | null } };

export type GetPlansQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPlansQuery = { __typename?: 'Query', getPlans?: Array<{ __typename?: 'Plan', id: string, name: string, price: number, remarks?: string | null, is_internal: boolean, category: string }> | null };

export type GetZonesAndPlansQueryVariables = Exact<{ [key: string]: never; }>;


export type GetZonesAndPlansQuery = { __typename?: 'Query', getZones?: Array<{ __typename?: 'Zone', id: string, name: string }> | null, getPlans?: Array<{ __typename?: 'Plan', id: string, name: string, price: number, remarks?: string | null, is_internal: boolean, category: string }> | null };

export type GetZonesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetZonesQuery = { __typename?: 'Query', getZones?: Array<{ __typename?: 'Zone', id: string, name: string }> | null };

export type GetCollectionScheduleNotificationStatsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetCollectionScheduleNotificationStatsQuery = { __typename?: 'Query', getCollectionScheduleNotificationStats: { __typename?: 'CollectionScheduleNotificationStatsPaginator', data?: Array<{ __typename?: 'CollectionScheduleNotificationStats', date: any, total: number, week_day: string, group_id: string }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type SendCollectionScheduleNotiToStaffMutationVariables = Exact<{
  sent_at: Scalars['DateTime']['input'];
  staff_phone_number: Scalars['String']['input'];
}>;


export type SendCollectionScheduleNotiToStaffMutation = { __typename?: 'Mutation', sendCollectionScheduleNotiToStaff: boolean };

export type GetCollectionScheduleQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  collection_day?: InputMaybe<CollectionDay>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
}>;


export type GetCollectionScheduleQuery = { __typename?: 'Query', getCollectionSchedule: { __typename?: 'CollectionSchedulePaginator', data?: Array<{ __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, collection_days?: Array<string> | null, alternate_phone_number?: string | null, plan: { __typename?: 'Plan', name: string } }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type SearchEstablishmentQueryVariables = Exact<{
  keyword: Scalars['String']['input'];
}>;


export type SearchEstablishmentQuery = { __typename?: 'Query', searchEstablishment?: Array<{ __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, collection_days?: Array<string> | null, zone?: { __typename?: 'Zone', id: string, name: string } | null, plan: { __typename?: 'Plan', id: string, name: string }, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null }> | null };

export type GetCollectionSentNotificationHistoriesQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  group_id: Scalars['String']['input'];
}>;


export type GetCollectionSentNotificationHistoriesQuery = { __typename?: 'Query', getCollectionSentNotificationHistories: { __typename?: 'CollectionSentNotificationPaginator', data?: Array<{ __typename?: 'CollectionScheduleNotification', id: string, week_day: string, establishment: { __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, collection_days?: Array<string> | null, alternate_phone_number?: string | null, plan: { __typename?: 'Plan', id: string, name: string } }, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type RegisterCustomerMutationVariables = Exact<{
  name: Scalars['String']['input'];
  address: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  plan_id: Scalars['ID']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
}>;


export type RegisterCustomerMutation = { __typename?: 'Mutation', registerCustomer: boolean };

export type AddCustomerMutationVariables = Exact<{
  name: Scalars['String']['input'];
  address: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
}>;


export type AddCustomerMutation = { __typename?: 'Mutation', addCustomer: boolean };

export type GetCustomersQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  customer_type?: InputMaybe<CustomerType>;
}>;


export type GetCustomersQuery = { __typename?: 'Query', getCustomers: { __typename?: 'CustomerListPaginator', data?: Array<{ __typename?: 'Customer', id: string, name: string, address: string, phone_number: string, created_at: any, verified_at?: any | null, remarks?: string | null, alternate_phone_number?: string | null, subscriptionHistory?: Array<{ __typename?: 'SubscriptionBill', id: string }> | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type GeneratePaymentReceiptLinkMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  phone_number?: InputMaybe<Scalars['String']['input']>;
  should_send: Scalars['Boolean']['input'];
}>;


export type GeneratePaymentReceiptLinkMutation = { __typename?: 'Mutation', generatePaymentReceiptLink: string };

export type ResendPaymentLinkMutationVariables = Exact<{
  customer_id?: InputMaybe<Scalars['ID']['input']>;
  subscription_id?: InputMaybe<Scalars['ID']['input']>;
  payable_amount?: InputMaybe<Scalars['Float']['input']>;
  phone_number: Scalars['String']['input'];
}>;


export type ResendPaymentLinkMutation = { __typename?: 'Mutation', resendPaymentLink: boolean };

export type MarkAsPaidMutationVariables = Exact<{
  subscription_id: Scalars['ID']['input'];
  payment_mode: PaymentMode;
  remarks?: InputMaybe<Scalars['String']['input']>;
  payment_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type MarkAsPaidMutation = { __typename?: 'Mutation', markAsPaid: boolean };

export type DeleteEstablishmentMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteEstablishmentMutation = { __typename?: 'Mutation', deleteEstablishment?: boolean | null };

export type AddSubscriptionMutationVariables = Exact<{
  customer_id: Scalars['ID']['input'];
  establishment_input: EstablishmentInput;
  subscription_input: SubscriptionInput;
}>;


export type AddSubscriptionMutation = { __typename?: 'Mutation', addSubscription: boolean };

export type GetCustomerSubscriptionHistoryQueryVariables = Exact<{
  customer_id: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetCustomerSubscriptionHistoryQuery = { __typename?: 'Query', getCustomerSubscriptionHistory: { __typename?: 'CustomerSubscriptionHistoryPaginator', data?: Array<{ __typename?: 'SubscriptionBill', id: string, start_date?: any | null, end_date?: any | null, payable_amount: number, payment_mode?: string | null, paid_at?: any | null, remarks?: string | null, customer: { __typename?: 'Customer', id: string, name: string, address: string, phone_number: string }, paymentOrder?: { __typename?: 'PaymentOrder', id: string, paymentable_type: string, paymentable: { __typename?: 'RzpayPaymentOrder', id: string, payment_status: string, payment_error?: string | null } } | null, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null, subscriptionItems?: Array<{ __typename?: 'SubscriptionBillItem', id: string, period: number, plan?: { __typename?: 'Plan', id: string, name: string, price: number } | null, establishment: { __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string } }> | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type GetCustomerEstablishmentsQueryVariables = Exact<{
  customer_id: Scalars['ID']['input'];
}>;


export type GetCustomerEstablishmentsQuery = { __typename?: 'Query', getCustomerEstablishment?: Array<{ __typename?: 'Establishment', id: string, name: string, phone_number: string, alt_phone_number?: string | null, address: string, remarks?: string | null, collection_days?: Array<string> | null, zone_id: string, current_plan_id: string, current_plan_period: number, subscription_status: string, plan: { __typename?: 'Plan', name: string } }> | null };

export type UpdateEstablishmentMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  alt_phone_number?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  collection_days?: InputMaybe<Array<CollectionDay> | CollectionDay>;
  current_plan_id?: InputMaybe<Scalars['ID']['input']>;
  current_plan_period?: InputMaybe<Scalars['Int']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
}>;


export type UpdateEstablishmentMutation = { __typename?: 'Mutation', updateEstablishment: boolean };

export type UpdateProductMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  item_name?: InputMaybe<Scalars['String']['input']>;
  stock?: InputMaybe<Scalars['Int']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  discount?: InputMaybe<Scalars['Float']['input']>;
}>;


export type UpdateProductMutation = { __typename?: 'Mutation', updateProduct: boolean };

export type GetProductsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetProductsQuery = { __typename?: 'Query', getProducts?: Array<{ __typename?: 'Product', id: string, item_name: string, stock: number, price: number, discount?: number | null }> | null };

export type SearchCustomerByNameQueryVariables = Exact<{
  name: Scalars['String']['input'];
}>;


export type SearchCustomerByNameQuery = { __typename?: 'Query', getCustomers: { __typename?: 'CustomerListPaginator', data?: Array<{ __typename?: 'Customer', id: string, name: string }> | null } };

export type AddSaleMutationVariables = Exact<{
  customer_id: Scalars['ID']['input'];
  payment_mode: PaymentMode;
  products: Array<ProductInput> | ProductInput;
}>;


export type AddSaleMutation = { __typename?: 'Mutation', addSale: boolean };

export type DeleteSaleMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteSaleMutation = { __typename?: 'Mutation', deleteSale: { __typename?: 'Sale', id: string } };

export type GetSalesQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  product_name?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetSalesQuery = { __typename?: 'Query', getSales: { __typename?: 'GetSalesPaginator', data?: Array<{ __typename?: 'Sale', id: string, payment_mode: string, created_at: any, customer: { __typename?: 'Customer', id: string, name: string, address: string, phone_number: string }, productSales: Array<{ __typename?: 'ProductSale', id: string, quantity: number, price_per_unit: number, discount?: number | null, product: { __typename?: 'Product', id: string, item_name: string } }> }> | null, paginator_info: { __typename?: 'AppPaginator', last_page: number } } };

export type GetPaymentRecordsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  month?: InputMaybe<Scalars['Int']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  is_paid?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GetPaymentRecordsQuery = { __typename?: 'Query', getPaymentRecords: { __typename?: 'GetPaymentRecordPaginator', data?: Array<{ __typename?: 'SubscriptionBill', id: string, start_date?: any | null, end_date?: any | null, payable_amount: number, paid_at?: any | null, payment_mode?: string | null, customer: { __typename?: 'Customer', id: string, name: string, phone_number: string, address: string }, subscriptionItems?: Array<{ __typename?: 'SubscriptionBillItem', id: string, plan?: { __typename?: 'Plan', id: string, name: string } | null, establishment: { __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, zone?: { __typename?: 'Zone', id: string, name: string } | null } }> | null, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type AddPlanMutationVariables = Exact<{
  name: Scalars['String']['input'];
  price: Scalars['Float']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  is_internal: Scalars['Boolean']['input'];
  category: PlanCategory;
}>;


export type AddPlanMutation = { __typename?: 'Mutation', addPlan: boolean };

export type UpdatePlanMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  is_internal?: InputMaybe<Scalars['Boolean']['input']>;
  category?: InputMaybe<PlanCategory>;
}>;


export type UpdatePlanMutation = { __typename?: 'Mutation', updatePlan: boolean };

export type DeletePlanMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeletePlanMutation = { __typename?: 'Mutation', deletePlan: boolean };

export type SubscriberPlanDistributionQueryVariables = Exact<{
  zone_id?: InputMaybe<Scalars['ID']['input']>;
}>;


export type SubscriberPlanDistributionQuery = { __typename?: 'Query', subscriberPlanDistribution?: Array<{ __typename?: 'SubscriberPlanDistribution', plan_name: string, total_subscriber: number }> | null };

export type SubscriberGrowthTrendQueryVariables = Exact<{
  year: Scalars['Int']['input'];
}>;


export type SubscriberGrowthTrendQuery = { __typename?: 'Query', subscriberGrowthTrend?: Array<{ __typename?: 'SubscriberGrowthTrend', month: number, total_subscriber: number }> | null };

export type SubscriptionPaymentTrendQueryVariables = Exact<{
  month: Scalars['Int']['input'];
  year: Scalars['Int']['input'];
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
}>;


export type SubscriptionPaymentTrendQuery = { __typename?: 'Query', subscriptionPaymentTrend?: Array<{ __typename?: 'SubscriptionPaymentTrend', day: number, total_amount: number }> | null };

export type GetBasicStatsQueryVariables = Exact<{
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetBasicStatsQuery = { __typename?: 'Query', getBasicStats: { __typename?: 'BasicStatsResponse', total_subscriber?: number | null, total_subscription_fee?: number | null, total_fee_paid?: number | null, total_fee_unpaid?: number | null } };

export type ProcessSubscriptionPaymentLinkMutationVariables = Exact<{
  payment_link_hash: Scalars['String']['input'];
}>;


export type ProcessSubscriptionPaymentLinkMutation = { __typename?: 'Mutation', processSubscriptionPaymentLink: { __typename?: 'RzpayPaymentOrder', id: string, order_id: string, payment_error?: string | null, payment_id?: string | null, payment_instrument_type?: string | null, payment_status: string, refund_amount?: string | null, refund_error?: string | null, refund_status?: string | null } };


export const AdminLoginDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AdminLogin"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"username"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"password"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"adminLogin"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"username"},"value":{"kind":"Variable","name":{"kind":"Name","value":"username"}}},{"kind":"Argument","name":{"kind":"Name","value":"password"},"value":{"kind":"Variable","name":{"kind":"Name","value":"password"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"user"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"username"}},{"kind":"Field","name":{"kind":"Name","value":"role"}}]}},{"kind":"Field","name":{"kind":"Name","value":"exp"}},{"kind":"Field","name":{"kind":"Name","value":"token"}}]}}]}}]} as unknown as DocumentNode<AdminLoginMutation, AdminLoginMutationVariables>;
export const DeleteCustomerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteCustomer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteCustomer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}]}]}}]} as unknown as DocumentNode<DeleteCustomerMutation, DeleteCustomerMutationVariables>;
export const LogoutDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"Logout"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"logout"}}]}}]} as unknown as DocumentNode<LogoutMutation, LogoutMutationVariables>;
export const SendCollectionScheduleNotificationDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"SendCollectionScheduleNotification"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_ids"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"week_day"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"sendCollectionScheduleNotiToEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"establishment_ids"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_ids"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}},{"kind":"Argument","name":{"kind":"Name","value":"week_day"},"value":{"kind":"Variable","name":{"kind":"Name","value":"week_day"}}}]}]}}]} as unknown as DocumentNode<SendCollectionScheduleNotificationMutation, SendCollectionScheduleNotificationMutationVariables>;
export const UpdateCustomerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateCustomer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"alternate_phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateCustomer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"alternate_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"alternate_phone_number"}}}]}]}}]} as unknown as DocumentNode<UpdateCustomerMutation, UpdateCustomerMutationVariables>;
export const CheckPaymentStatusDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"CheckPaymentStatus"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"order_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"payment_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"checkPaymentStatus"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"order_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"order_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"payment_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"payment_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_receipt_link"}},{"kind":"Field","name":{"kind":"Name","value":"payable_amount"}},{"kind":"Field","name":{"kind":"Name","value":"paid_at"}}]}}]}}]} as unknown as DocumentNode<CheckPaymentStatusQuery, CheckPaymentStatusQueryVariables>;
export const GetPlansDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPlans"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPlans"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}},{"kind":"Field","name":{"kind":"Name","value":"is_internal"}},{"kind":"Field","name":{"kind":"Name","value":"category"}}]}}]}}]} as unknown as DocumentNode<GetPlansQuery, GetPlansQueryVariables>;
export const GetZonesAndPlansDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetZonesAndPlans"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getZones"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"getPlans"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}},{"kind":"Field","name":{"kind":"Name","value":"is_internal"}},{"kind":"Field","name":{"kind":"Name","value":"category"}}]}}]}}]} as unknown as DocumentNode<GetZonesAndPlansQuery, GetZonesAndPlansQueryVariables>;
export const GetZonesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetZones"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getZones"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]} as unknown as DocumentNode<GetZonesQuery, GetZonesQueryVariables>;
export const GetCollectionScheduleNotificationStatsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCollectionScheduleNotificationStats"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCollectionScheduleNotificationStats"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"week_day"}},{"kind":"Field","name":{"kind":"Name","value":"group_id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"current_page"}},{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetCollectionScheduleNotificationStatsQuery, GetCollectionScheduleNotificationStatsQueryVariables>;
export const SendCollectionScheduleNotiToStaffDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"SendCollectionScheduleNotiToStaff"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"sent_at"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"staff_phone_number"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"sendCollectionScheduleNotiToStaff"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"sent_at"},"value":{"kind":"Variable","name":{"kind":"Name","value":"sent_at"}}},{"kind":"Argument","name":{"kind":"Name","value":"staff_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"staff_phone_number"}}}]}]}}]} as unknown as DocumentNode<SendCollectionScheduleNotiToStaffMutation, SendCollectionScheduleNotiToStaffMutationVariables>;
export const GetCollectionScheduleDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCollectionSchedule"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"collection_day"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"CollectionDay"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subscription_status"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"SubscriptionStatus"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCollectionSchedule"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"collection_day"},"value":{"kind":"Variable","name":{"kind":"Name","value":"collection_day"}}},{"kind":"Argument","name":{"kind":"Name","value":"subscription_status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subscription_status"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","alias":{"kind":"Name","value":"alternate_phone_number"},"name":{"kind":"Name","value":"alt_phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"collection_days"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"current_page"}},{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetCollectionScheduleQuery, GetCollectionScheduleQueryVariables>;
export const SearchEstablishmentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SearchEstablishment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"searchEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"zone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"collection_days"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"latestWhatsApp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"message_status"}}]}}]}}]}}]} as unknown as DocumentNode<SearchEstablishmentQuery, SearchEstablishmentQueryVariables>;
export const GetCollectionSentNotificationHistoriesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCollectionSentNotificationHistories"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"group_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCollectionSentNotificationHistories"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"group_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"group_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"establishment"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","alias":{"kind":"Name","value":"alternate_phone_number"},"name":{"kind":"Name","value":"alt_phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"collection_days"}}]}},{"kind":"Field","name":{"kind":"Name","value":"week_day"}},{"kind":"Field","name":{"kind":"Name","value":"latestWhatsApp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"message_status"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"current_page"}},{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetCollectionSentNotificationHistoriesQuery, GetCollectionSentNotificationHistoriesQueryVariables>;
export const RegisterCustomerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RegisterCustomer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"alternate_phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"registerCustomer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"alternate_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"alternate_phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"plan_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}}]}]}}]} as unknown as DocumentNode<RegisterCustomerMutation, RegisterCustomerMutationVariables>;
export const AddCustomerDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddCustomer"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"alternate_phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addCustomer"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"alternate_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"alternate_phone_number"}}}]}]}}]} as unknown as DocumentNode<AddCustomerMutation, AddCustomerMutationVariables>;
export const GetCustomersDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"getCustomers"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subscription_status"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"SubscriptionStatus"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"customer_type"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"CustomerType"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCustomers"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"subscription_status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subscription_status"}}},{"kind":"Argument","name":{"kind":"Name","value":"plan_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"zone_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"customer_type"},"value":{"kind":"Variable","name":{"kind":"Name","value":"customer_type"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","alias":{"kind":"Name","value":"alternate_phone_number"},"name":{"kind":"Name","value":"alt_phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"created_at"}},{"kind":"Field","name":{"kind":"Name","value":"verified_at"}},{"kind":"Field","name":{"kind":"Name","value":"subscriptionHistory"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"current_page"}},{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetCustomersQuery, GetCustomersQueryVariables>;
export const GeneratePaymentReceiptLinkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"GeneratePaymentReceiptLink"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"should_send"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"generatePaymentReceiptLink"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"should_send"},"value":{"kind":"Variable","name":{"kind":"Name","value":"should_send"}}}]}]}}]} as unknown as DocumentNode<GeneratePaymentReceiptLinkMutation, GeneratePaymentReceiptLinkMutationVariables>;
export const ResendPaymentLinkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"ResendPaymentLink"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subscription_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"payable_amount"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"resendPaymentLink"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"subscription_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subscription_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"customer_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"payable_amount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"payable_amount"}}}]}]}}]} as unknown as DocumentNode<ResendPaymentLinkMutation, ResendPaymentLinkMutationVariables>;
export const MarkAsPaidDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"MarkAsPaid"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subscription_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"payment_mode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"PaymentMode"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"payment_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"markAsPaid"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"subscription_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subscription_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"payment_mode"},"value":{"kind":"Variable","name":{"kind":"Name","value":"payment_mode"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}},{"kind":"Argument","name":{"kind":"Name","value":"payment_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"payment_date"}}}]}]}}]} as unknown as DocumentNode<MarkAsPaidMutation, MarkAsPaidMutationVariables>;
export const DeleteEstablishmentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteEstablishment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}]}]}}]} as unknown as DocumentNode<DeleteEstablishmentMutation, DeleteEstablishmentMutationVariables>;
export const AddSubscriptionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddSubscription"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"establishment_input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"EstablishmentInput"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subscription_input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"SubscriptionInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addSubscription"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"customer_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"establishment_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"establishment_input"}}},{"kind":"Argument","name":{"kind":"Name","value":"subscription_input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subscription_input"}}}]}]}}]} as unknown as DocumentNode<AddSubscriptionMutation, AddSubscriptionMutationVariables>;
export const GetCustomerSubscriptionHistoryDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCustomerSubscriptionHistory"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCustomerSubscriptionHistory"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"customer_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"payable_amount"}},{"kind":"Field","name":{"kind":"Name","value":"customer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"payment_mode"}},{"kind":"Field","name":{"kind":"Name","value":"paymentOrder"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"paymentable_type"}},{"kind":"Field","name":{"kind":"Name","value":"paymentable"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RzpayPaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_status"}},{"kind":"Field","name":{"kind":"Name","value":"payment_error"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"latestWhatsApp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"message_status"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paid_at"}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}},{"kind":"Field","name":{"kind":"Name","value":"subscriptionItems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"price"}}]}},{"kind":"Field","name":{"kind":"Name","value":"establishment"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"period"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"current_page"}},{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetCustomerSubscriptionHistoryQuery, GetCustomerSubscriptionHistoryQueryVariables>;
export const GetCustomerEstablishmentsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetCustomerEstablishments"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCustomerEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"customer_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"alt_phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"remarks"}},{"kind":"Field","name":{"kind":"Name","value":"collection_days"}},{"kind":"Field","name":{"kind":"Name","value":"zone_id"}},{"kind":"Field","name":{"kind":"Name","value":"current_plan_id"}},{"kind":"Field","name":{"kind":"Name","value":"current_plan_period"}},{"kind":"Field","name":{"kind":"Name","value":"subscription_status"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]} as unknown as DocumentNode<GetCustomerEstablishmentsQuery, GetCustomerEstablishmentsQueryVariables>;
export const UpdateEstablishmentDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateEstablishment"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"alt_phone_number"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"address"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"collection_days"}},"type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CollectionDay"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"current_plan_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"current_plan_period"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"subscription_status"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"SubscriptionStatus"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateEstablishment"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"alt_phone_number"},"value":{"kind":"Variable","name":{"kind":"Name","value":"alt_phone_number"}}},{"kind":"Argument","name":{"kind":"Name","value":"address"},"value":{"kind":"Variable","name":{"kind":"Name","value":"address"}}},{"kind":"Argument","name":{"kind":"Name","value":"collection_days"},"value":{"kind":"Variable","name":{"kind":"Name","value":"collection_days"}}},{"kind":"Argument","name":{"kind":"Name","value":"current_plan_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"current_plan_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"current_plan_period"},"value":{"kind":"Variable","name":{"kind":"Name","value":"current_plan_period"}}},{"kind":"Argument","name":{"kind":"Name","value":"zone_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"subscription_status"},"value":{"kind":"Variable","name":{"kind":"Name","value":"subscription_status"}}}]}]}}]} as unknown as DocumentNode<UpdateEstablishmentMutation, UpdateEstablishmentMutationVariables>;
export const UpdateProductDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdateProduct"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"item_name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"stock"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"price"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"discount"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateProduct"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"item_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"item_name"}}},{"kind":"Argument","name":{"kind":"Name","value":"stock"},"value":{"kind":"Variable","name":{"kind":"Name","value":"stock"}}},{"kind":"Argument","name":{"kind":"Name","value":"price"},"value":{"kind":"Variable","name":{"kind":"Name","value":"price"}}},{"kind":"Argument","name":{"kind":"Name","value":"discount"},"value":{"kind":"Variable","name":{"kind":"Name","value":"discount"}}}]}]}}]} as unknown as DocumentNode<UpdateProductMutation, UpdateProductMutationVariables>;
export const GetProductsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetProducts"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getProducts"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"item_name"}},{"kind":"Field","name":{"kind":"Name","value":"stock"}},{"kind":"Field","name":{"kind":"Name","value":"price"}},{"kind":"Field","name":{"kind":"Name","value":"discount"}}]}}]}}]} as unknown as DocumentNode<GetProductsQuery, GetProductsQueryVariables>;
export const SearchCustomerByNameDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SearchCustomerByName"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getCustomers"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"IntValue","value":"20"}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}}]} as unknown as DocumentNode<SearchCustomerByNameQuery, SearchCustomerByNameQueryVariables>;
export const AddSaleDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddSale"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"payment_mode"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"PaymentMode"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"products"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ProductInput"}}}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addSale"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"customer_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"customer_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"payment_mode"},"value":{"kind":"Variable","name":{"kind":"Name","value":"payment_mode"}}},{"kind":"Argument","name":{"kind":"Name","value":"products"},"value":{"kind":"Variable","name":{"kind":"Name","value":"products"}}}]}]}}]} as unknown as DocumentNode<AddSaleMutation, AddSaleMutationVariables>;
export const DeleteSaleDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeleteSale"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deleteSale"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]} as unknown as DocumentNode<DeleteSaleMutation, DeleteSaleMutationVariables>;
export const GetSalesDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetSales"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"product_name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getSales"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"product_name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"product_name"}}},{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_mode"}},{"kind":"Field","name":{"kind":"Name","value":"customer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}}]}},{"kind":"Field","name":{"kind":"Name","value":"productSales"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"quantity"}},{"kind":"Field","name":{"kind":"Name","value":"price_per_unit"}},{"kind":"Field","name":{"kind":"Name","value":"discount"}},{"kind":"Field","name":{"kind":"Name","value":"product"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"item_name"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"created_at"}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetSalesQuery, GetSalesQueryVariables>;
export const GetPaymentRecordsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetPaymentRecords"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"first"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"month"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"year"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"page"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_paid"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getPaymentRecords"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"first"},"value":{"kind":"Variable","name":{"kind":"Name","value":"first"}}},{"kind":"Argument","name":{"kind":"Name","value":"page"},"value":{"kind":"Variable","name":{"kind":"Name","value":"page"}}},{"kind":"Argument","name":{"kind":"Name","value":"month"},"value":{"kind":"Variable","name":{"kind":"Name","value":"month"}}},{"kind":"Argument","name":{"kind":"Name","value":"year"},"value":{"kind":"Variable","name":{"kind":"Name","value":"year"}}},{"kind":"Argument","name":{"kind":"Name","value":"keyword"},"value":{"kind":"Variable","name":{"kind":"Name","value":"keyword"}}},{"kind":"Argument","name":{"kind":"Name","value":"plan_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_paid"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_paid"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"data"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"start_date"}},{"kind":"Field","name":{"kind":"Name","value":"end_date"}},{"kind":"Field","name":{"kind":"Name","value":"payable_amount"}},{"kind":"Field","name":{"kind":"Name","value":"customer"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"address"}}]}},{"kind":"Field","name":{"kind":"Name","value":"subscriptionItems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"plan"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}},{"kind":"Field","name":{"kind":"Name","value":"establishment"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"address"}},{"kind":"Field","name":{"kind":"Name","value":"phone_number"}},{"kind":"Field","name":{"kind":"Name","value":"zone"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}}]}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paid_at"}},{"kind":"Field","name":{"kind":"Name","value":"payment_mode"}},{"kind":"Field","name":{"kind":"Name","value":"latestWhatsApp"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"message_status"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"paginator_info"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total"}},{"kind":"Field","name":{"kind":"Name","value":"current_page"}},{"kind":"Field","name":{"kind":"Name","value":"last_page"}}]}}]}}]}}]} as unknown as DocumentNode<GetPaymentRecordsQuery, GetPaymentRecordsQueryVariables>;
export const AddPlanDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"AddPlan"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"price"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_internal"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"category"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"PlanCategory"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"addPlan"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"price"},"value":{"kind":"Variable","name":{"kind":"Name","value":"price"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_internal"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_internal"}}},{"kind":"Argument","name":{"kind":"Name","value":"category"},"value":{"kind":"Variable","name":{"kind":"Name","value":"category"}}}]}]}}]} as unknown as DocumentNode<AddPlanMutation, AddPlanMutationVariables>;
export const UpdatePlanDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"UpdatePlan"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"name"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"price"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Float"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"is_internal"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"Boolean"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"category"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"PlanCategory"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updatePlan"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}},{"kind":"Argument","name":{"kind":"Name","value":"name"},"value":{"kind":"Variable","name":{"kind":"Name","value":"name"}}},{"kind":"Argument","name":{"kind":"Name","value":"price"},"value":{"kind":"Variable","name":{"kind":"Name","value":"price"}}},{"kind":"Argument","name":{"kind":"Name","value":"remarks"},"value":{"kind":"Variable","name":{"kind":"Name","value":"remarks"}}},{"kind":"Argument","name":{"kind":"Name","value":"is_internal"},"value":{"kind":"Variable","name":{"kind":"Name","value":"is_internal"}}},{"kind":"Argument","name":{"kind":"Name","value":"category"},"value":{"kind":"Variable","name":{"kind":"Name","value":"category"}}}]}]}}]} as unknown as DocumentNode<UpdatePlanMutation, UpdatePlanMutationVariables>;
export const DeletePlanDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"DeletePlan"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"id"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"deletePlan"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"id"}}}]}]}}]} as unknown as DocumentNode<DeletePlanMutation, DeletePlanMutationVariables>;
export const SubscriberPlanDistributionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SubscriberPlanDistribution"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"subscriberPlanDistribution"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"zone_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"plan_name"}},{"kind":"Field","name":{"kind":"Name","value":"total_subscriber"}}]}}]}}]} as unknown as DocumentNode<SubscriberPlanDistributionQuery, SubscriberPlanDistributionQueryVariables>;
export const SubscriberGrowthTrendDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SubscriberGrowthTrend"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"year"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"subscriberGrowthTrend"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"year"},"value":{"kind":"Variable","name":{"kind":"Name","value":"year"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"month"}},{"kind":"Field","name":{"kind":"Name","value":"total_subscriber"}}]}}]}}]} as unknown as DocumentNode<SubscriberGrowthTrendQuery, SubscriberGrowthTrendQueryVariables>;
export const SubscriptionPaymentTrendDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"SubscriptionPaymentTrend"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"month"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"year"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"Int"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"subscriptionPaymentTrend"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"month"},"value":{"kind":"Variable","name":{"kind":"Name","value":"month"}}},{"kind":"Argument","name":{"kind":"Name","value":"year"},"value":{"kind":"Variable","name":{"kind":"Name","value":"year"}}},{"kind":"Argument","name":{"kind":"Name","value":"plan_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"plan_id"}}},{"kind":"Argument","name":{"kind":"Name","value":"zone_id"},"value":{"kind":"Variable","name":{"kind":"Name","value":"zone_id"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"day"}},{"kind":"Field","name":{"kind":"Name","value":"total_amount"}}]}}]}}]} as unknown as DocumentNode<SubscriptionPaymentTrendQuery, SubscriptionPaymentTrendQueryVariables>;
export const GetBasicStatsDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"GetBasicStats"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"DateTime"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getBasicStats"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"start_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"start_date"}}},{"kind":"Argument","name":{"kind":"Name","value":"end_date"},"value":{"kind":"Variable","name":{"kind":"Name","value":"end_date"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"total_subscriber"}},{"kind":"Field","name":{"kind":"Name","value":"total_subscription_fee"}},{"kind":"Field","name":{"kind":"Name","value":"total_fee_paid"}},{"kind":"Field","name":{"kind":"Name","value":"total_fee_unpaid"}}]}}]}}]} as unknown as DocumentNode<GetBasicStatsQuery, GetBasicStatsQueryVariables>;
export const ProcessSubscriptionPaymentLinkDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"ProcessSubscriptionPaymentLink"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"payment_link_hash"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"processSubscriptionPaymentLink"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"payment_link_hash"},"value":{"kind":"Variable","name":{"kind":"Name","value":"payment_link_hash"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"InlineFragment","typeCondition":{"kind":"NamedType","name":{"kind":"Name","value":"RzpayPaymentOrder"}},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"order_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_error"}},{"kind":"Field","name":{"kind":"Name","value":"payment_id"}},{"kind":"Field","name":{"kind":"Name","value":"payment_instrument_type"}},{"kind":"Field","name":{"kind":"Name","value":"payment_status"}},{"kind":"Field","name":{"kind":"Name","value":"refund_amount"}},{"kind":"Field","name":{"kind":"Name","value":"refund_error"}},{"kind":"Field","name":{"kind":"Name","value":"refund_status"}}]}}]}}]}}]} as unknown as DocumentNode<ProcessSubscriptionPaymentLinkMutation, ProcessSubscriptionPaymentLinkMutationVariables>;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  /** A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`. */
  DateTime: { input: any; output: any; }
};

export type AdminLoginResponse = {
  __typename?: 'AdminLoginResponse';
  exp: Scalars['DateTime']['output'];
  token: Scalars['String']['output'];
  user: User;
};

export type AppPaginator = {
  __typename?: 'AppPaginator';
  current_page: Scalars['Int']['output'];
  has_more_pages: Scalars['Boolean']['output'];
  last_page: Scalars['Int']['output'];
  total: Scalars['Int']['output'];
};

export type BasicStatsResponse = {
  __typename?: 'BasicStatsResponse';
  total_fee_paid?: Maybe<Scalars['Float']['output']>;
  total_fee_unpaid?: Maybe<Scalars['Float']['output']>;
  total_subscriber?: Maybe<Scalars['Int']['output']>;
  total_subscription_fee?: Maybe<Scalars['Float']['output']>;
};

export enum CollectionDay {
  Friday = 'FRIDAY',
  Monday = 'MONDAY',
  Saturday = 'SATURDAY',
  Sunday = 'SUNDAY',
  Thursday = 'THURSDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY'
}

export type CollectionScheduleNotification = {
  __typename?: 'CollectionScheduleNotification';
  establishment: Establishment;
  establishment_id: Scalars['ID']['output'];
  group_id: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  latestWhatsApp?: Maybe<WhatsAppReceipt>;
  sent_at?: Maybe<Scalars['DateTime']['output']>;
  week_day: Scalars['String']['output'];
};

export type CollectionScheduleNotificationStats = {
  __typename?: 'CollectionScheduleNotificationStats';
  date: Scalars['DateTime']['output'];
  group_id: Scalars['String']['output'];
  total: Scalars['Int']['output'];
  week_day: Scalars['String']['output'];
};

export type CollectionScheduleNotificationStatsPaginator = {
  __typename?: 'CollectionScheduleNotificationStatsPaginator';
  data?: Maybe<Array<CollectionScheduleNotificationStats>>;
  paginator_info: AppPaginator;
};

export type CollectionSchedulePaginator = {
  __typename?: 'CollectionSchedulePaginator';
  data?: Maybe<Array<Establishment>>;
  paginator_info: AppPaginator;
};

export type CollectionSentNotificationPaginator = {
  __typename?: 'CollectionSentNotificationPaginator';
  data?: Maybe<Array<CollectionScheduleNotification>>;
  paginator_info: AppPaginator;
};

export type Customer = {
  __typename?: 'Customer';
  address: Scalars['String']['output'];
  alt_phone_number?: Maybe<Scalars['String']['output']>;
  created_at: Scalars['DateTime']['output'];
  establishments?: Maybe<Array<Establishment>>;
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
  phone_number: Scalars['String']['output'];
  remarks?: Maybe<Scalars['String']['output']>;
  subscriptionHistory?: Maybe<Array<SubscriptionBill>>;
  /** is officially a customer when verified */
  verified_at?: Maybe<Scalars['DateTime']['output']>;
};

export type CustomerListPaginator = {
  __typename?: 'CustomerListPaginator';
  data?: Maybe<Array<Customer>>;
  paginator_info: AppPaginator;
};

export type CustomerSubscriptionHistoryPaginator = {
  __typename?: 'CustomerSubscriptionHistoryPaginator';
  data?: Maybe<Array<SubscriptionBill>>;
  paginator_info: AppPaginator;
};

export enum CustomerType {
  Approved = 'APPROVED',
  PendingApproval = 'PENDING_APPROVAL'
}

export type Establishment = {
  __typename?: 'Establishment';
  address: Scalars['String']['output'];
  alt_phone_number?: Maybe<Scalars['String']['output']>;
  collection_days?: Maybe<Array<Scalars['String']['output']>>;
  current_plan_id: Scalars['ID']['output'];
  current_plan_period: Scalars['Int']['output'];
  customer: Customer;
  id: Scalars['ID']['output'];
  latestWhatsApp?: Maybe<WhatsAppReceipt>;
  name: Scalars['String']['output'];
  phone_number: Scalars['String']['output'];
  plan: Plan;
  remarks?: Maybe<Scalars['String']['output']>;
  subscription_status: Scalars['String']['output'];
  zone?: Maybe<Zone>;
  zone_id: Scalars['ID']['output'];
};

export type EstablishmentInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  alt_phone_number?: InputMaybe<Scalars['String']['input']>;
  collection_days?: InputMaybe<Array<Scalars['String']['input']>>;
  current_plan_id?: InputMaybe<Scalars['ID']['input']>;
  /** for updating existing establishment */
  id?: InputMaybe<Scalars['ID']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};

export type GetPaymentRecordPaginator = {
  __typename?: 'GetPaymentRecordPaginator';
  data?: Maybe<Array<SubscriptionBill>>;
  paginator_info: AppPaginator;
};

export type GetSalesPaginator = {
  __typename?: 'GetSalesPaginator';
  data?: Maybe<Array<Sale>>;
  paginator_info: AppPaginator;
};

export type Mutation = {
  __typename?: 'Mutation';
  /** customer are automatically verified */
  addCustomer: Scalars['Boolean']['output'];
  addPlan: Scalars['Boolean']['output'];
  addSale: Scalars['Boolean']['output'];
  /** for offline susbcription */
  addSubscription: Scalars['Boolean']['output'];
  adminLogin: AdminLoginResponse;
  deleteCustomer: Scalars['Boolean']['output'];
  deleteEstablishment?: Maybe<Scalars['Boolean']['output']>;
  deletePlan: Scalars['Boolean']['output'];
  deleteSale: Sale;
  deleteSubscription: Scalars['Boolean']['output'];
  generatePaymentReceiptLink: Scalars['String']['output'];
  logout: Scalars['Boolean']['output'];
  markAsPaid: Scalars['Boolean']['output'];
  processSubscriptionPaymentLink: PaymentableOrder;
  /** for public user registration */
  registerCustomer: Scalars['Boolean']['output'];
  resendPaymentLink: Scalars['Boolean']['output'];
  /** send notification to customer regarding collection schedule */
  sendCollectionScheduleNotiToEstablishment: Scalars['Boolean']['output'];
  sendCollectionScheduleNotiToStaff: Scalars['Boolean']['output'];
  updateCustomer: Scalars['Boolean']['output'];
  updateEstablishment: Scalars['Boolean']['output'];
  updatePlan: Scalars['Boolean']['output'];
  updateProduct: Scalars['Boolean']['output'];
  updateSubscription: Scalars['Boolean']['output'];
};


export type MutationAddCustomerArgs = {
  address: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
};


export type MutationAddPlanArgs = {
  category: PlanCategory;
  is_internal: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  price: Scalars['Float']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
};


export type MutationAddSaleArgs = {
  customer_id: Scalars['ID']['input'];
  payment_mode: PaymentMode;
  products: Array<ProductInput>;
};


export type MutationAddSubscriptionArgs = {
  customer_id: Scalars['ID']['input'];
  establishment_input: EstablishmentInput;
  subscription_input: SubscriptionInput;
};


export type MutationAdminLoginArgs = {
  password: Scalars['String']['input'];
  username: Scalars['String']['input'];
};


export type MutationDeleteCustomerArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteEstablishmentArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeletePlanArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSaleArgs = {
  id: Scalars['ID']['input'];
};


export type MutationDeleteSubscriptionArgs = {
  id: Scalars['Int']['input'];
};


export type MutationGeneratePaymentReceiptLinkArgs = {
  id: Scalars['ID']['input'];
  phone_number?: InputMaybe<Scalars['String']['input']>;
  should_send: Scalars['Boolean']['input'];
};


export type MutationMarkAsPaidArgs = {
  payment_date?: InputMaybe<Scalars['DateTime']['input']>;
  payment_mode: PaymentMode;
  remarks?: InputMaybe<Scalars['String']['input']>;
  subscription_id: Scalars['ID']['input'];
};


export type MutationProcessSubscriptionPaymentLinkArgs = {
  payment_link_hash: Scalars['String']['input'];
};


export type MutationRegisterCustomerArgs = {
  address: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  plan_id: Scalars['ID']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
};


export type MutationResendPaymentLinkArgs = {
  customer_id?: InputMaybe<Scalars['ID']['input']>;
  payable_amount?: InputMaybe<Scalars['Float']['input']>;
  phone_number: Scalars['String']['input'];
  subscription_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationSendCollectionScheduleNotiToEstablishmentArgs = {
  establishment_ids?: InputMaybe<Array<Scalars['ID']['input']>>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  week_day: Scalars['String']['input'];
};


export type MutationSendCollectionScheduleNotiToStaffArgs = {
  sent_at: Scalars['DateTime']['input'];
  staff_phone_number: Scalars['String']['input'];
};


export type MutationUpdateCustomerArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateEstablishmentArgs = {
  address?: InputMaybe<Scalars['String']['input']>;
  alt_phone_number?: InputMaybe<Scalars['String']['input']>;
  collection_days?: InputMaybe<Array<CollectionDay>>;
  current_plan_id?: InputMaybe<Scalars['ID']['input']>;
  current_plan_period?: InputMaybe<Scalars['Int']['input']>;
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};


export type MutationUpdatePlanArgs = {
  category?: InputMaybe<PlanCategory>;
  id: Scalars['ID']['input'];
  is_internal?: InputMaybe<Scalars['Boolean']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateProductArgs = {
  discount?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['ID']['input'];
  item_name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  stock?: InputMaybe<Scalars['Int']['input']>;
};


export type MutationUpdateSubscriptionArgs = {
  id: Scalars['ID']['input'];
  paid_at?: InputMaybe<Scalars['DateTime']['input']>;
  payable_amount?: InputMaybe<Scalars['Float']['input']>;
  payment_mode?: InputMaybe<PaymentMode>;
  payment_remarks?: InputMaybe<Scalars['String']['input']>;
  period?: InputMaybe<Scalars['Int']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
};

/** Allows ordering a list of records. */
export type OrderByClause = {
  /** The column that is used for ordering. */
  column: Scalars['String']['input'];
  /** The direction that is used for ordering. */
  order: SortOrder;
};

/** Aggregate functions when ordering by a relation without specifying a column. */
export enum OrderByRelationAggregateFunction {
  /** Amount of items. */
  Count = 'COUNT'
}

/** Aggregate functions when ordering by a relation that may specify a column. */
export enum OrderByRelationWithColumnAggregateFunction {
  /** Average. */
  Avg = 'AVG',
  /** Amount of items. */
  Count = 'COUNT',
  /** Maximum. */
  Max = 'MAX',
  /** Minimum. */
  Min = 'MIN',
  /** Sum. */
  Sum = 'SUM'
}

export enum PaymentMode {
  Card = 'CARD',
  Cash = 'CASH',
  Cheque = 'CHEQUE',
  Paymentlink = 'PAYMENTLINK',
  Upi = 'UPI'
}

export type PaymentOrder = {
  __typename?: 'PaymentOrder';
  id: Scalars['ID']['output'];
  paymentable: PaymentableOrder;
  paymentable_id: Scalars['ID']['output'];
  paymentable_type: Scalars['String']['output'];
};

export type PaymentableOrder = RzpayPaymentOrder;

export type Plan = {
  __typename?: 'Plan';
  category: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  is_internal: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  remarks?: Maybe<Scalars['String']['output']>;
};

export enum PlanCategory {
  Commercial = 'COMMERCIAL',
  Domestic = 'DOMESTIC'
}

export type Product = {
  __typename?: 'Product';
  discount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  item_name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  stock: Scalars['Int']['output'];
};

export type ProductInput = {
  product_id: Scalars['ID']['input'];
  quantity: Scalars['Int']['input'];
};

export type ProductSale = {
  __typename?: 'ProductSale';
  discount?: Maybe<Scalars['Float']['output']>;
  id: Scalars['ID']['output'];
  price_per_unit: Scalars['Float']['output'];
  product: Product;
  product_id: Scalars['ID']['output'];
  quantity: Scalars['Int']['output'];
  sale_id: Scalars['ID']['output'];
};

/** Indicates what fields are available at the top level of a query operation. */
export type Query = {
  __typename?: 'Query';
  checkPaymentStatus: SubscriptionBill;
  customerById?: Maybe<Customer>;
  getBasicStats: BasicStatsResponse;
  /** customer list by collection days */
  getCollectionSchedule: CollectionSchedulePaginator;
  getCollectionScheduleNotificationStats: CollectionScheduleNotificationStatsPaginator;
  getCollectionSentNotificationHistories: CollectionSentNotificationPaginator;
  getCustomerEstablishment?: Maybe<Array<Establishment>>;
  getCustomerSubscriptionHistory: CustomerSubscriptionHistoryPaginator;
  getCustomers: CustomerListPaginator;
  getPaymentReceiptLink?: Maybe<Scalars['String']['output']>;
  getPaymentRecords: GetPaymentRecordPaginator;
  getPlans?: Maybe<Array<Plan>>;
  getProducts?: Maybe<Array<Product>>;
  getSales: GetSalesPaginator;
  getZones?: Maybe<Array<Zone>>;
  searchEstablishment?: Maybe<Array<Establishment>>;
  subscriberGrowthTrend?: Maybe<Array<SubscriberGrowthTrend>>;
  subscriberPlanDistribution?: Maybe<Array<SubscriberPlanDistribution>>;
  subscriptionPaymentTrend?: Maybe<Array<SubscriptionPaymentTrend>>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCheckPaymentStatusArgs = {
  order_id: Scalars['String']['input'];
  payment_id: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryCustomerByIdArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetBasicStatsArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCollectionScheduleArgs = {
  collection_day?: InputMaybe<CollectionDay>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCollectionScheduleNotificationStatsArgs = {
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCollectionSentNotificationHistoriesArgs = {
  first: Scalars['Int']['input'];
  group_id: Scalars['String']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCustomerEstablishmentArgs = {
  customer_id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCustomerSubscriptionHistoryArgs = {
  customer_id: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetCustomersArgs = {
  customer_type?: InputMaybe<CustomerType>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  first: Scalars['Int']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPaymentReceiptLinkArgs = {
  id: Scalars['ID']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetPaymentRecordsArgs = {
  first: Scalars['Int']['input'];
  is_paid?: InputMaybe<Scalars['Boolean']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  month?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QueryGetSalesArgs = {
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  product_name?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySearchEstablishmentArgs = {
  keyword: Scalars['String']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySubscriberGrowthTrendArgs = {
  year: Scalars['Int']['input'];
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySubscriberPlanDistributionArgs = {
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};


/** Indicates what fields are available at the top level of a query operation. */
export type QuerySubscriptionPaymentTrendArgs = {
  month: Scalars['Int']['input'];
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  year: Scalars['Int']['input'];
  zone_id?: InputMaybe<Scalars['ID']['input']>;
};

export type RzpayPaymentOrder = {
  __typename?: 'RzpayPaymentOrder';
  id: Scalars['ID']['output'];
  order_id: Scalars['String']['output'];
  payment_error?: Maybe<Scalars['String']['output']>;
  payment_id?: Maybe<Scalars['String']['output']>;
  payment_instrument_type?: Maybe<Scalars['String']['output']>;
  payment_status: Scalars['String']['output'];
  refund_amount?: Maybe<Scalars['String']['output']>;
  refund_error?: Maybe<Scalars['String']['output']>;
  refund_status?: Maybe<Scalars['String']['output']>;
};

export type Sale = {
  __typename?: 'Sale';
  created_at: Scalars['DateTime']['output'];
  customer: Customer;
  customer_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  payment_mode: Scalars['String']['output'];
  productSales: Array<ProductSale>;
  products: Array<Product>;
};

/** Directions for ordering a list of records. */
export enum SortOrder {
  /** Sort records in ascending order. */
  Asc = 'ASC',
  /** Sort records in descending order. */
  Desc = 'DESC'
}

export type SubscriberGrowthTrend = {
  __typename?: 'SubscriberGrowthTrend';
  month: Scalars['Int']['output'];
  total_subscriber: Scalars['Int']['output'];
};

export type SubscriberPlanDistribution = {
  __typename?: 'SubscriberPlanDistribution';
  plan_name: Scalars['String']['output'];
  total_subscriber: Scalars['Int']['output'];
};

export type SubscriptionBill = {
  __typename?: 'SubscriptionBill';
  customer: Customer;
  customer_id: Scalars['ID']['output'];
  end_date?: Maybe<Scalars['DateTime']['output']>;
  id: Scalars['ID']['output'];
  latestWhatsApp?: Maybe<WhatsAppReceipt>;
  overdue: Scalars['Float']['output'];
  paid_at?: Maybe<Scalars['DateTime']['output']>;
  payable_amount: Scalars['Float']['output'];
  paymentOrder?: Maybe<PaymentOrder>;
  payment_link_hash?: Maybe<Scalars['String']['output']>;
  payment_mode?: Maybe<Scalars['String']['output']>;
  payment_receipt_link?: Maybe<Scalars['String']['output']>;
  remarks?: Maybe<Scalars['String']['output']>;
  start_date?: Maybe<Scalars['DateTime']['output']>;
  subscriptionItems?: Maybe<Array<SubscriptionBillItem>>;
  whatsapp_number: Scalars['String']['output'];
  whatsappable?: Maybe<Array<WhatsAppReceipt>>;
};

export type SubscriptionBillItem = {
  __typename?: 'SubscriptionBillItem';
  establishment: Establishment;
  establishment_id: Scalars['ID']['output'];
  id: Scalars['ID']['output'];
  period: Scalars['Int']['output'];
  plan?: Maybe<Plan>;
  plan_id?: Maybe<Scalars['ID']['output']>;
  price: Scalars['Float']['output'];
  subscriptionBill: SubscriptionBill;
  subscription_bill_id: Scalars['ID']['output'];
};

export type SubscriptionInput = {
  /** for updating existing subscription */
  id?: InputMaybe<Scalars['ID']['input']>;
  /** default is 1, i.e 1 month */
  period?: InputMaybe<Scalars['Int']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
};

export type SubscriptionPaymentTrend = {
  __typename?: 'SubscriptionPaymentTrend';
  day: Scalars['Int']['output'];
  total_amount: Scalars['Float']['output'];
};

export enum SubscriptionStatus {
  Active = 'ACTIVE',
  Inactive = 'INACTIVE'
}

/** Specify if you want to include or exclude trashed results from a query. */
export enum Trashed {
  /** Only return trashed results. */
  Only = 'ONLY',
  /** Return both trashed and non-trashed results. */
  With = 'WITH',
  /** Only return non-trashed results. */
  Without = 'WITHOUT'
}

/** Account of a person who uses this application. */
export type User = {
  __typename?: 'User';
  /** When the account was created. */
  created_at: Scalars['DateTime']['output'];
  /** Unique primary key. */
  id: Scalars['ID']['output'];
  role: Scalars['String']['output'];
  /** When the account was last updated. */
  updated_at: Scalars['DateTime']['output'];
  username: Scalars['String']['output'];
};

export type WhatsAppReceipt = {
  __typename?: 'WhatsAppReceipt';
  created_at: Scalars['DateTime']['output'];
  error_message?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  message_status: Scalars['String']['output'];
  purpose?: Maybe<Scalars['String']['output']>;
  template_name: Scalars['String']['output'];
  updated_at: Scalars['DateTime']['output'];
  wamid: Scalars['String']['output'];
};

export type Zone = {
  __typename?: 'Zone';
  id: Scalars['ID']['output'];
  name: Scalars['String']['output'];
};

export type AdminLoginMutationVariables = Exact<{
  username: Scalars['String']['input'];
  password: Scalars['String']['input'];
}>;


export type AdminLoginMutation = { __typename?: 'Mutation', adminLogin: { __typename?: 'AdminLoginResponse', exp: any, token: string, user: { __typename?: 'User', id: string, username: string, role: string } } };

export type DeleteCustomerMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteCustomerMutation = { __typename?: 'Mutation', deleteCustomer: boolean };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type SendCollectionScheduleNotificationMutationVariables = Exact<{
  establishment_ids?: InputMaybe<Array<Scalars['ID']['input']> | Scalars['ID']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  week_day: Scalars['String']['input'];
}>;


export type SendCollectionScheduleNotificationMutation = { __typename?: 'Mutation', sendCollectionScheduleNotiToEstablishment: boolean };

export type UpdateCustomerMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateCustomerMutation = { __typename?: 'Mutation', updateCustomer: boolean };

export type CheckPaymentStatusQueryVariables = Exact<{
  order_id: Scalars['String']['input'];
  payment_id: Scalars['String']['input'];
}>;


export type CheckPaymentStatusQuery = { __typename?: 'Query', checkPaymentStatus: { __typename?: 'SubscriptionBill', id: string, payment_receipt_link?: string | null, payable_amount: number, paid_at?: any | null } };

export type GetPlansQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPlansQuery = { __typename?: 'Query', getPlans?: Array<{ __typename?: 'Plan', id: string, name: string, price: number, remarks?: string | null, is_internal: boolean, category: string }> | null };

export type GetZonesAndPlansQueryVariables = Exact<{ [key: string]: never; }>;


export type GetZonesAndPlansQuery = { __typename?: 'Query', getZones?: Array<{ __typename?: 'Zone', id: string, name: string }> | null, getPlans?: Array<{ __typename?: 'Plan', id: string, name: string, price: number, remarks?: string | null, is_internal: boolean, category: string }> | null };

export type GetZonesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetZonesQuery = { __typename?: 'Query', getZones?: Array<{ __typename?: 'Zone', id: string, name: string }> | null };

export type GetCollectionScheduleNotificationStatsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetCollectionScheduleNotificationStatsQuery = { __typename?: 'Query', getCollectionScheduleNotificationStats: { __typename?: 'CollectionScheduleNotificationStatsPaginator', data?: Array<{ __typename?: 'CollectionScheduleNotificationStats', date: any, total: number, week_day: string, group_id: string }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type SendCollectionScheduleNotiToStaffMutationVariables = Exact<{
  sent_at: Scalars['DateTime']['input'];
  staff_phone_number: Scalars['String']['input'];
}>;


export type SendCollectionScheduleNotiToStaffMutation = { __typename?: 'Mutation', sendCollectionScheduleNotiToStaff: boolean };

export type GetCollectionScheduleQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  collection_day?: InputMaybe<CollectionDay>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
}>;


export type GetCollectionScheduleQuery = { __typename?: 'Query', getCollectionSchedule: { __typename?: 'CollectionSchedulePaginator', data?: Array<{ __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, collection_days?: Array<string> | null, alternate_phone_number?: string | null, plan: { __typename?: 'Plan', name: string } }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type SearchEstablishmentQueryVariables = Exact<{
  keyword: Scalars['String']['input'];
}>;


export type SearchEstablishmentQuery = { __typename?: 'Query', searchEstablishment?: Array<{ __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, collection_days?: Array<string> | null, zone?: { __typename?: 'Zone', id: string, name: string } | null, plan: { __typename?: 'Plan', id: string, name: string }, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null }> | null };

export type GetCollectionSentNotificationHistoriesQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  group_id: Scalars['String']['input'];
}>;


export type GetCollectionSentNotificationHistoriesQuery = { __typename?: 'Query', getCollectionSentNotificationHistories: { __typename?: 'CollectionSentNotificationPaginator', data?: Array<{ __typename?: 'CollectionScheduleNotification', id: string, week_day: string, establishment: { __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, collection_days?: Array<string> | null, alternate_phone_number?: string | null, plan: { __typename?: 'Plan', id: string, name: string } }, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type RegisterCustomerMutationVariables = Exact<{
  name: Scalars['String']['input'];
  address: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
  plan_id: Scalars['ID']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
}>;


export type RegisterCustomerMutation = { __typename?: 'Mutation', registerCustomer: boolean };

export type AddCustomerMutationVariables = Exact<{
  name: Scalars['String']['input'];
  address: Scalars['String']['input'];
  phone_number: Scalars['String']['input'];
  alternate_phone_number?: InputMaybe<Scalars['String']['input']>;
}>;


export type AddCustomerMutation = { __typename?: 'Mutation', addCustomer: boolean };

export type GetCustomersQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  customer_type?: InputMaybe<CustomerType>;
}>;


export type GetCustomersQuery = { __typename?: 'Query', getCustomers: { __typename?: 'CustomerListPaginator', data?: Array<{ __typename?: 'Customer', id: string, name: string, address: string, phone_number: string, created_at: any, verified_at?: any | null, remarks?: string | null, alternate_phone_number?: string | null, subscriptionHistory?: Array<{ __typename?: 'SubscriptionBill', id: string }> | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type GeneratePaymentReceiptLinkMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  phone_number?: InputMaybe<Scalars['String']['input']>;
  should_send: Scalars['Boolean']['input'];
}>;


export type GeneratePaymentReceiptLinkMutation = { __typename?: 'Mutation', generatePaymentReceiptLink: string };

export type ResendPaymentLinkMutationVariables = Exact<{
  customer_id?: InputMaybe<Scalars['ID']['input']>;
  subscription_id?: InputMaybe<Scalars['ID']['input']>;
  payable_amount?: InputMaybe<Scalars['Float']['input']>;
  phone_number: Scalars['String']['input'];
}>;


export type ResendPaymentLinkMutation = { __typename?: 'Mutation', resendPaymentLink: boolean };

export type MarkAsPaidMutationVariables = Exact<{
  subscription_id: Scalars['ID']['input'];
  payment_mode: PaymentMode;
  remarks?: InputMaybe<Scalars['String']['input']>;
  payment_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type MarkAsPaidMutation = { __typename?: 'Mutation', markAsPaid: boolean };

export type DeleteEstablishmentMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteEstablishmentMutation = { __typename?: 'Mutation', deleteEstablishment?: boolean | null };

export type AddSubscriptionMutationVariables = Exact<{
  customer_id: Scalars['ID']['input'];
  establishment_input: EstablishmentInput;
  subscription_input: SubscriptionInput;
}>;


export type AddSubscriptionMutation = { __typename?: 'Mutation', addSubscription: boolean };

export type GetCustomerSubscriptionHistoryQueryVariables = Exact<{
  customer_id: Scalars['ID']['input'];
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
}>;


export type GetCustomerSubscriptionHistoryQuery = { __typename?: 'Query', getCustomerSubscriptionHistory: { __typename?: 'CustomerSubscriptionHistoryPaginator', data?: Array<{ __typename?: 'SubscriptionBill', id: string, start_date?: any | null, end_date?: any | null, payable_amount: number, payment_mode?: string | null, paid_at?: any | null, remarks?: string | null, customer: { __typename?: 'Customer', id: string, name: string, address: string, phone_number: string }, paymentOrder?: { __typename?: 'PaymentOrder', id: string, paymentable_type: string, paymentable: { __typename?: 'RzpayPaymentOrder', id: string, payment_status: string, payment_error?: string | null } } | null, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null, subscriptionItems?: Array<{ __typename?: 'SubscriptionBillItem', id: string, period: number, plan?: { __typename?: 'Plan', id: string, name: string, price: number } | null, establishment: { __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string } }> | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type GetCustomerEstablishmentsQueryVariables = Exact<{
  customer_id: Scalars['ID']['input'];
}>;


export type GetCustomerEstablishmentsQuery = { __typename?: 'Query', getCustomerEstablishment?: Array<{ __typename?: 'Establishment', id: string, name: string, phone_number: string, alt_phone_number?: string | null, address: string, remarks?: string | null, collection_days?: Array<string> | null, zone_id: string, current_plan_id: string, current_plan_period: number, subscription_status: string, plan: { __typename?: 'Plan', name: string } }> | null };

export type UpdateEstablishmentMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  phone_number?: InputMaybe<Scalars['String']['input']>;
  alt_phone_number?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<Scalars['String']['input']>;
  collection_days?: InputMaybe<Array<CollectionDay> | CollectionDay>;
  current_plan_id?: InputMaybe<Scalars['ID']['input']>;
  current_plan_period?: InputMaybe<Scalars['Int']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
  subscription_status?: InputMaybe<SubscriptionStatus>;
}>;


export type UpdateEstablishmentMutation = { __typename?: 'Mutation', updateEstablishment: boolean };

export type UpdateProductMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  item_name?: InputMaybe<Scalars['String']['input']>;
  stock?: InputMaybe<Scalars['Int']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  discount?: InputMaybe<Scalars['Float']['input']>;
}>;


export type UpdateProductMutation = { __typename?: 'Mutation', updateProduct: boolean };

export type GetProductsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetProductsQuery = { __typename?: 'Query', getProducts?: Array<{ __typename?: 'Product', id: string, item_name: string, stock: number, price: number, discount?: number | null }> | null };

export type SearchCustomerByNameQueryVariables = Exact<{
  name: Scalars['String']['input'];
}>;


export type SearchCustomerByNameQuery = { __typename?: 'Query', getCustomers: { __typename?: 'CustomerListPaginator', data?: Array<{ __typename?: 'Customer', id: string, name: string }> | null } };

export type AddSaleMutationVariables = Exact<{
  customer_id: Scalars['ID']['input'];
  payment_mode: PaymentMode;
  products: Array<ProductInput> | ProductInput;
}>;


export type AddSaleMutation = { __typename?: 'Mutation', addSale: boolean };

export type DeleteSaleMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeleteSaleMutation = { __typename?: 'Mutation', deleteSale: { __typename?: 'Sale', id: string } };

export type GetSalesQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  page?: InputMaybe<Scalars['Int']['input']>;
  product_name?: InputMaybe<Scalars['String']['input']>;
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetSalesQuery = { __typename?: 'Query', getSales: { __typename?: 'GetSalesPaginator', data?: Array<{ __typename?: 'Sale', id: string, payment_mode: string, created_at: any, customer: { __typename?: 'Customer', id: string, name: string, address: string, phone_number: string }, productSales: Array<{ __typename?: 'ProductSale', id: string, quantity: number, price_per_unit: number, discount?: number | null, product: { __typename?: 'Product', id: string, item_name: string } }> }> | null, paginator_info: { __typename?: 'AppPaginator', last_page: number } } };

export type GetPaymentRecordsQueryVariables = Exact<{
  first: Scalars['Int']['input'];
  month?: InputMaybe<Scalars['Int']['input']>;
  year?: InputMaybe<Scalars['Int']['input']>;
  page?: InputMaybe<Scalars['Int']['input']>;
  keyword?: InputMaybe<Scalars['String']['input']>;
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  is_paid?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type GetPaymentRecordsQuery = { __typename?: 'Query', getPaymentRecords: { __typename?: 'GetPaymentRecordPaginator', data?: Array<{ __typename?: 'SubscriptionBill', id: string, start_date?: any | null, end_date?: any | null, payable_amount: number, paid_at?: any | null, payment_mode?: string | null, customer: { __typename?: 'Customer', id: string, name: string, phone_number: string, address: string }, subscriptionItems?: Array<{ __typename?: 'SubscriptionBillItem', id: string, plan?: { __typename?: 'Plan', id: string, name: string } | null, establishment: { __typename?: 'Establishment', id: string, name: string, address: string, phone_number: string, zone?: { __typename?: 'Zone', id: string, name: string } | null } }> | null, latestWhatsApp?: { __typename?: 'WhatsAppReceipt', id: string, message_status: string } | null }> | null, paginator_info: { __typename?: 'AppPaginator', total: number, current_page: number, last_page: number } } };

export type AddPlanMutationVariables = Exact<{
  name: Scalars['String']['input'];
  price: Scalars['Float']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  is_internal: Scalars['Boolean']['input'];
  category: PlanCategory;
}>;


export type AddPlanMutation = { __typename?: 'Mutation', addPlan: boolean };

export type UpdatePlanMutationVariables = Exact<{
  id: Scalars['ID']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  remarks?: InputMaybe<Scalars['String']['input']>;
  is_internal?: InputMaybe<Scalars['Boolean']['input']>;
  category?: InputMaybe<PlanCategory>;
}>;


export type UpdatePlanMutation = { __typename?: 'Mutation', updatePlan: boolean };

export type DeletePlanMutationVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DeletePlanMutation = { __typename?: 'Mutation', deletePlan: boolean };

export type SubscriberPlanDistributionQueryVariables = Exact<{
  zone_id?: InputMaybe<Scalars['ID']['input']>;
}>;


export type SubscriberPlanDistributionQuery = { __typename?: 'Query', subscriberPlanDistribution?: Array<{ __typename?: 'SubscriberPlanDistribution', plan_name: string, total_subscriber: number }> | null };

export type SubscriberGrowthTrendQueryVariables = Exact<{
  year: Scalars['Int']['input'];
}>;


export type SubscriberGrowthTrendQuery = { __typename?: 'Query', subscriberGrowthTrend?: Array<{ __typename?: 'SubscriberGrowthTrend', month: number, total_subscriber: number }> | null };

export type SubscriptionPaymentTrendQueryVariables = Exact<{
  month: Scalars['Int']['input'];
  year: Scalars['Int']['input'];
  plan_id?: InputMaybe<Scalars['ID']['input']>;
  zone_id?: InputMaybe<Scalars['ID']['input']>;
}>;


export type SubscriptionPaymentTrendQuery = { __typename?: 'Query', subscriptionPaymentTrend?: Array<{ __typename?: 'SubscriptionPaymentTrend', day: number, total_amount: number }> | null };

export type GetBasicStatsQueryVariables = Exact<{
  start_date?: InputMaybe<Scalars['DateTime']['input']>;
  end_date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetBasicStatsQuery = { __typename?: 'Query', getBasicStats: { __typename?: 'BasicStatsResponse', total_subscriber?: number | null, total_subscription_fee?: number | null, total_fee_paid?: number | null, total_fee_unpaid?: number | null } };

export type ProcessSubscriptionPaymentLinkMutationVariables = Exact<{
  payment_link_hash: Scalars['String']['input'];
}>;


export type ProcessSubscriptionPaymentLinkMutation = { __typename?: 'Mutation', processSubscriptionPaymentLink: { __typename?: 'RzpayPaymentOrder', id: string, order_id: string, payment_error?: string | null, payment_id?: string | null, payment_instrument_type?: string | null, payment_status: string, refund_amount?: string | null, refund_error?: string | null, refund_status?: string | null } };
