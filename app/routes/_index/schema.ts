import z from 'zod'

export const registerCustomerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  address: z.string().min(1, 'Address is required'),
  phone_number: z.string().min(10, 'Phone number must be 10 digits').max(10, 'Phone number must be 10 digits'),
  alternate_phone_number: z.string().optional(),
  plan_id: z.string().min(1, 'Plan is required'),
  remarks: z.string(),
})

export type RegisterCustomerType = z.infer<typeof registerCustomerSchema>
