import type { RegisterCustomerType } from './schema'
import type { Plan } from '~/gql/graphql'
import { toast } from 'sonner'
import FormMessage from '~/components/common/form-message'
import { Button } from '~/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { useAppForm } from '~/hooks/form'
import useBoolean from '~/hooks/use-boolean'
import { registerCustomerSchema } from './schema'
import useRegisterCustomer from './use-register-customer'

interface Props {
  plans: Plan[]
}

export default function SubscribeDialog({ plans }: Props) {
  const { isOpen, toggle } = useBoolean()
  const { registerCustomer } = useRegisterCustomer()

  const form = useAppForm({
    defaultValues: {
      name: '',
      phone_number: '',
      address: '',
      alternate_phone_number: '',
      plan_id: '',
      remarks: '',
    } as RegisterCustomerType,
    validators: {
      onSubmit: registerCustomerSchema,
    },
    onSubmit: async ({ value }) => {
      const formValues = { ...value }
      formValues.remarks = plans.find(p => p.id === value.plan_id)?.name || ''
      await registerCustomer.mutateAsync(formValues, {
        onSuccess: () => {
          toggle(false)
          form.reset()
          toast.success('Thank you for registering!. Our team will get back to you soon.')
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogTrigger asChild>
        <div className="mt-8 flex justify-center">
          <Button
            className={`
              w-80 bg-blue-500 py-6 text-white
              hover:bg-blue-500/80
            `}
            onClick={() => {
              toggle(true)
            }}
          >
            Subscribe
          </Button>

        </div>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Let's Get You Started
          </DialogTitle>
          <DialogDescription>
            Our onboarding team will contact you shortly after submitting this form
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-y-4"
        >
          <form.AppField
            name="name"
            children={field => <field.InputField label="Name" />}
          />
          <form.AppField
            name="phone_number"
            children={field => <field.MobileInputField label="Phone Number" />}
          />
          <form.AppField
            name="alternate_phone_number"
            children={field => <field.MobileInputField label="Alternate Phone Number (Optional)" />}
          />
          <form.AppField
            name="address"
            children={field => <field.InputField label="Address" />}
          />
          <form.AppField
            name="plan_id" // remarks is actually the plan name here
            children={field => (
              <>
                <Label className="flex flex-col items-start gap-y-2">
                  <div>Plan</div>
                  <Select value={field.state.value} onValueChange={field.handleChange}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select plan" />
                    </SelectTrigger>
                    <SelectContent>
                      {plans.map((plan) => {
                        return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
                      })}
                    </SelectContent>
                  </Select>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </Label>
              </>
            )}
          />
          {/* <form.AppField */}
          {/*   name="plan_id" // don't need to show this but it's required so sr-only */}
          {/*   children={field => ( */}
          {/*     <> */}
          {/*       <Label */}
          {/*         tabIndex={-1} */}
          {/*         className="sr-only flex flex-col items-start gap-y-2" */}
          {/*       > */}
          {/*         <div>Plan</div> */}
          {/*         <Select value={field.state.value} onValueChange={field.handleChange}> */}
          {/*           <SelectTrigger className="w-full"> */}
          {/*             <SelectValue /> */}
          {/*           </SelectTrigger> */}
          {/*           <SelectContent> */}
          {/*             {plans.map((plan) => { */}
          {/*               return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem> */}
          {/*             })} */}
          {/*           </SelectContent> */}
          {/*         </Select> */}
          {/*         <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} /> */}
          {/*       </Label> */}
          {/*     </> */}
          {/*   )} */}
          {/* /> */}
          <form.AppForm>
            <form.SubmitButton label="Submit" />
          </form.AppForm>
        </form>
      </DialogContent>
    </Dialog>
  )
}
