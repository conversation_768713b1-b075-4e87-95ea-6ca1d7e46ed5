import type { Route } from './+types/route'
import { useLoaderData } from 'react-router'
import BackButton from '~/components/common/back-button'
import PageHeader from '~/components/common/page-header'
import { GET_ZONES_AND_PLANS } from '~/graphql/queries/get-zones-and-plans'
import { graphqlClient } from '~/lib/graphql-client'
import { getSession } from '~/sessions'
import AddSubscriptionDialog from './add-subscription-dialog'
import CustomerEstablishments from './customer-establishments'
import SubscriptionHistoryTable from './subscription-history-table'

export async function loader({ params, request }: Route.LoaderArgs) {
  const id = params.id
  const session = await getSession(request.headers.get('Cookie'))
  const token = session.get('token')
  const client = await graphqlClient({ token })

  // TODO: Get customerById from here
  // then pass it to addsubscriptiondialog to initially fill the establishment details with customer info

  const data = await client.request({
    document: GET_ZONES_AND_PLANS,
  })

  const zones = data.getZones || []
  const plans = data.getPlans || []

  return {
    zones,
    plans,
    id,
  }
}

export default function CustomerById() {
  const { id, zones, plans } = useLoaderData<typeof loader>()

  return (
    <>
      <div className="flex grow flex-col gap-4">
        <BackButton />
        <div className="flex items-center justify-between">
          <PageHeader title="Purchase history" />
          <AddSubscriptionDialog zones={zones} plans={plans} customer_id={id} />
        </div>
        <CustomerEstablishments zones={zones} plans={plans} id={id} />
        <SubscriptionHistoryTable customerId={id} />
      </div>
    </>
  )
}
