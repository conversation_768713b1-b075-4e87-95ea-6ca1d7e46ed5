import type { SendCollectionScheduleNotiToStaffType } from './schema'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { SEND_COLLECTION_SCHEDULE_NOTI } from './graphql'

export default function useSendCollectionScheduleNotiToStaff() {
  const sendCollectionScheduleNotiToStaff = useMutation({
    mutationFn: async (data: SendCollectionScheduleNotiToStaffType) => {
      const client = await graphqlClient()
      return client.request({
        document: SEND_COLLECTION_SCHEDULE_NOTI,
        variables: {
          ...data,
        },
      })
    },
    onSuccess: () => {
      toast.success('Collection schedule notification sent successfully')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { sendCollectionScheduleNotiToStaff }
}
