import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import { sendCollectionScheduleNotiToStaffSchema } from './schema'
import useSendCollectionScheduleNotiToStaff from './use-send-collection-schedule-noti-to-staff'

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  sentDate: string
}

export default function SendCollectionScheduleToStaffDialog({ isOpen, toggle, sentDate }: Props) {
  const { sendCollectionScheduleNotiToStaff } = useSendCollectionScheduleNotiToStaff()
  const form = useAppForm({
    defaultValues: {
      sent_at: sentDate,
      staff_phone_number: '',
    },
    validators: {
      onSubmit: sendCollectionScheduleNotiToStaffSchema,
    },
    onSubmit: async ({ value }) => {
      await sendCollectionScheduleNotiToStaff.mutateAsync(value, {
        onSuccess: () => {
          toggle(false)
          form.reset()
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Send Collection Schedule to Staff
          </DialogTitle>
          <DialogDescription>
            Date:
            {' '}
            {sentDate}
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-y-4"
        >
          <form.AppField
            name="staff_phone_number"
            children={field => <field.MobileInputField label="Staff Phone Number" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Send" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>

    </Dialog>
  )
}
