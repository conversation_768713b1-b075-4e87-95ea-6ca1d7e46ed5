import { format } from 'date-fns'
import { useState } from 'react'
import { useNavigate } from 'react-router'
import PagePagination from '~/components/common/page-pagination'
import EyeIcon from '~/components/icons/eye-icon'
import MessageIcon from '~/components/icons/message-icon'
import { Button } from '~/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import SendCollectionScheduleToStaffDialog from './send-collection-schedule-to-staff-dialog'
import useGetCollectionScheduleNotificationStats from './use-get-collection-schedule-notification-stats'

export default function CollectionScheduleNotificationStatsTable() {
  const { isOpen, toggle } = useBoolean()

  const [selectedSchedule, setSelectedSchedule] = useState('')

  const { data, isLoading, isError, page, handlePage, lastPage } = useGetCollectionScheduleNotificationStats()
  const navigate = useNavigate()

  return (
    <>

      <div className={`
        mt-4 flex w-full grow overflow-x-auto rounded-md bg-white p-2
      `}
      >
        <Table className="w-full min-w-[1000px] table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[200px]">Date</TableHead>
              <TableHead className="w-[500px]">No of recipients</TableHead>
              <TableHead className="w-[300px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? (
                  <TableRow>
                    <TableCell colSpan={3} className="py-8 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )
              : isError
                ? (
                    <TableRow>
                      <TableCell
                        colSpan={3}
                        className="py-8 text-center text-red-500"
                      >
                        Error loading data
                      </TableCell>
                    </TableRow>
                  )
                : data?.getCollectionScheduleNotificationStats?.data?.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={3} className="py-8 text-center">
                          No records found
                        </TableCell>
                      </TableRow>
                    )
                  : (
                      data?.getCollectionScheduleNotificationStats?.data?.map(record => (
                        <TableRow key={record.date}>
                          <TableCell className="truncate">
                            {format(new Date(record.date), 'EEEE, do MMM yyyy')}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.total}
                          </TableCell>
                          <TableCell>
                            <div className="flex justify-end gap-x-4">
                              <Button
                                onClick={() => {
                                  navigate(`/admin/collection-schedules/details/${record.group_id}`)
                                }}
                                size="icon"
                                variant="outline"
                              >
                                <EyeIcon />
                              </Button>
                              <Button
                                onClick={() => {
                                  setSelectedSchedule(record.date)
                                  toggle(true)
                                }}
                                size="icon"
                                variant="outline"
                              >
                                <MessageIcon />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
          </TableBody>
        </Table>
      </div>
      {lastPage > 1 && (
        <div className="flex justify-center">
          <PagePagination
            lastPage={lastPage}
            currentPage={page}
            handlePagePagination={handlePage}
          />
        </div>
      )}
      {selectedSchedule && (
        <SendCollectionScheduleToStaffDialog
          isOpen={isOpen}
          toggle={toggle}
          sentDate={selectedSchedule}
        />
      )}
    </>
  )
}
