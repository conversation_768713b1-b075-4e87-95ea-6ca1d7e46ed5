import { graphql } from '~/gql'

export const GET_COLLECTION_SCHEDULE_NOTIFICATION_STATS = graphql(`
  query GetCollectionScheduleNotificationStats($first: Int!, $page: Int) {
    getCollectionScheduleNotificationStats(first: $first, page: $page) {
      data {
        date
        total
        week_day
        group_id
      }
      paginator_info {
        total
        current_page
        last_page
      }
    }
  }
`)

export const SEND_COLLECTION_SCHEDULE_NOTI = graphql(`
  mutation SendCollectionScheduleNotiToStaff(
    $sent_at: DateTime!
    $staff_phone_number: String!
  ) {
    sendCollectionScheduleNotiToStaff(sent_at: $sent_at, staff_phone_number: $staff_phone_number)
  }
`)
