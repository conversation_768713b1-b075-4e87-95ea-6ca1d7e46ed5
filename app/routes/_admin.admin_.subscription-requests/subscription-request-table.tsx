import type { Plan, Zone } from '~/gql/graphql'
import type { SelectedCustomer } from '~/lib/customer-schema'
import { format } from 'date-fns'
import { useState } from 'react'
import AppTooltip from '~/components/common/app-tooltip'
import ConfirmationDialog from '~/components/common/confirmation-dialog'
import PagePagination from '~/components/common/page-pagination'
import DeleteIcon from '~/components/icons/delete-icon'
import UpdateIcon from '~/components/icons/update-icon'
import { Button } from '~/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '~/components/ui/table'
import { CustomerType } from '~/gql/graphql'
import useBoolean from '~/hooks/use-boolean'
import useCustomerMutations from '~/hooks/use-customer-mutations'
import useGetCustomers from '~/hooks/use-get-customers'
import { cn } from '~/lib/utils'
import ApproveCustomerDialog from './approve-customer-dialog'

interface Props {
  zones: Zone[]
  plans: Plan[]
}

export default function SubscriptionRequestTable({ zones, plans }: Props) {
  const { isOpen: deleteOpen, toggle: toggleDelete } = useBoolean()
  const { isOpen: updateOpen, toggle: toggleUpdate } = useBoolean()

  const [selectedId, setSelectedId] = useState('')
  const [selectedCustomer, setSelectedCustomer] = useState<SelectedCustomer>()

  const { deleteCustomer } = useCustomerMutations()
  const { data, isLoading, isError, handlePage, page, lastPage }
    = useGetCustomers({ customerType: CustomerType.PendingApproval })

  const handleDelete = async () => {
    if (!selectedId)
      return
    await deleteCustomer.mutateAsync(selectedId, {
      onSuccess: () => {
        toggleDelete(false)
      },
    })
  }

  const handleUpdate = (customer: SelectedCustomer) => {
    setSelectedCustomer(customer)
    toggleUpdate(true)
  }

  return (
    <>
      <div
        className={`
          mt-4 flex w-full grow overflow-x-auto rounded-md bg-white p-2
        `}
      >
        <Table className="w-full min-w-[1000px] table-fixed">
          <TableHeader>
            <TableRow>
              <TableHead className="w-[150px]">Name</TableHead>
              <TableHead className="w-[340px]">Address</TableHead>
              <TableHead className="w-[140px]">Phone number</TableHead>
              <TableHead className="w-[140px]">Subscription Plan</TableHead>
              {/* <TableHead className="w-[140px]">Subscription Status</TableHead> */}
              <TableHead className="w-[140px]">Account Created On</TableHead>
              <TableHead className="w-[140px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? (
                  <TableRow>
                    <TableCell colSpan={6} className="py-8 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                )
              : isError
                ? (
                    <TableRow>
                      <TableCell
                        colSpan={6}
                        className="py-8 text-center text-red-500"
                      >
                        Error loading data
                      </TableCell>
                    </TableRow>
                  )
                : data?.getCustomers?.data?.length === 0
                  ? (
                      <TableRow>
                        <TableCell colSpan={6} className="py-8 text-center">
                          No records found
                        </TableCell>
                      </TableRow>
                    )
                  : (
                      data?.getCustomers?.data?.map(record => (
                        <TableRow key={record.id}>
                          <TableCell className="truncate">
                            {record.name || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.address || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.phone_number || '-'}
                          </TableCell>
                          <TableCell className="truncate">
                            {record.remarks || '-'}
                          </TableCell>
                          {/* <TableCell className={cn('truncate text-green-500', { */}
                          {/*   'text-destructive': !record.latestSubscription?.paid_at, */}
                          {/* })} */}
                          {/* > */}
                          {/*   {record.latestSubscription?.paid_at ? 'Active' : 'Inactive'} */}
                          {/* </TableCell> */}
                          <TableCell className="truncate">
                            {record.created_at
                              ? format(new Date(record.created_at), 'do MMM yy')
                              : '-'}
                          </TableCell>
                          <TableCell className="flex justify-end gap-x-4">
                            <AppTooltip message="Update">
                              <Button
                                onClick={() => {
                                  handleUpdate(record)
                                }}
                                size="icon"
                                variant="outline"
                              >
                                <UpdateIcon className="text-green-700" />
                              </Button>
                            </AppTooltip>
                            <AppTooltip message="Delete">
                              <Button
                                onClick={() => {
                                  setSelectedId(record.id)
                                  toggleDelete(true)
                                }}
                                size="icon"
                                variant="outline"
                              >
                                <DeleteIcon className="text-destructive" />
                              </Button>
                            </AppTooltip>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
          </TableBody>
        </Table>
      </div>
      {lastPage > 1 && (
        <div className="flex justify-center">
          <PagePagination
            lastPage={lastPage}
            currentPage={page}
            handlePagePagination={handlePage}
          />
        </div>
      )}
      {selectedId && (
        <ConfirmationDialog
          isPending={deleteCustomer.isPending}
          handleConfirm={handleDelete}
          open={deleteOpen}
          handleOpenChange={toggleDelete}
        />
      )}
      {selectedCustomer && (
        <ApproveCustomerDialog
          zones={zones}
          customer={selectedCustomer}
          isOpen={updateOpen}
          handleOpenChange={toggleUpdate}
          plans={plans}
        />
      )}
    </>
  )
}
