import type { ApproveCustomerType } from './schema'
import type { Plan, Zone } from '~/gql/graphql'
import type { SelectedCustomer } from '~/lib/customer-schema'
import FormMessage from '~/components/common/form-message'
import MonthYearPicker from '~/components/common/month-year-picker'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { SubscriptionStatus } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import useCustomerMutations from '~/hooks/use-customer-mutations'
import { approveCustomerSchema } from './schema'

interface Props {
  zones: Zone[]
  isOpen: boolean
  handleOpenChange: (open: boolean) => void
  customer: SelectedCustomer
  plans: Plan[]
}

export default function ApproveCustomerDialog({ plans, zones, isOpen, handleOpenChange, customer }: Props) {
  const { updateCustomer } = useCustomerMutations()
  const form = useAppForm({
    defaultValues: {
      id: customer.id,
      name: customer.name,
      address: customer.address,
      phone_number: customer.phone_number,
      zone_id: '',
      collection_days: [],
      alternate_phone_number: customer.alt_phone_number || '',
      period: '' as unknown as number,
      plan_id: '',
      subscription_status: SubscriptionStatus.Active, // auto to active since you're approving the customer
      start_date: '',
      customer_remarks: '',
    } as ApproveCustomerType,
    validators: {
      onSubmit: approveCustomerSchema,
    },
    onSubmit: async ({ value }) => {
      await updateCustomer.mutateAsync(value, {
        onSuccess: () => {
          handleOpenChange(false)
          setTimeout(() => {
            form.reset()
          }, 1000)
          // form.reset()
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="w-full min-w-4xl">
        <DialogHeader>
          <DialogTitle>
            Add customer
          </DialogTitle>
          <DialogDescription>
            Enter customer details
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="grid grid-cols-2 gap-x-8 gap-y-4"
        >
          <div className="col-span-1">
            <form.AppField
              name="name"
              children={field => <field.InputField label="Name" />}
            />
          </div>

          <div className="col-span-1">
            <form.AppField
              name="address"
              children={field => <field.InputField label="Address" />}
            />
          </div>

          <div className="col-span-1">
            <form.AppField
              name="phone_number"
              children={field => <field.MobileInputField label="Phone Number" />}
            />
          </div>

          <div className="col-span-1">
            <form.AppField
              name="alternate_phone_number"
              children={field => <field.MobileInputField label="Alternate Phone Number" />}
            />
          </div>

          <div className="col-span-2 my-2 w-full border" />

          <div className="col-span-1">
            <form.AppField
              name="period"
              children={field => <field.NumberField label="Billing cycle" />}
            />
          </div>

          <div className="col-span-1">
            <form.AppField
              name="start_date"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>
                      Start Date
                    </div>
                    <MonthYearPicker value={field.state.value} onChange={field.handleChange} />
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>

          <div className="col-span-1">
            <form.AppField
              name="plan_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Subscription Plan</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {plans.map((plan) => {
                          return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>
          <div className="col-span-1">
            <form.AppField
              name="zone_id"
              children={field => (
                <>
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Zone</div>
                    <Select value={field.state.value} onValueChange={field.handleChange}>
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {zones.map((zone) => {
                          return <SelectItem key={zone.id} value={zone.id}>{zone.name}</SelectItem>
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                </>
              )}
            />
          </div>

          <div className="col-span-2">
            <form.AppField
              name="customer_remarks"
              children={field => <field.TextareaField label="Remarks" />}
            />
          </div>
          <div className="col-span-2">
            <form.AppField
              name="collection_days"
              children={field => (
                <div className="flex flex-col gap-y-2">
                  <Label>Collection Days</Label>
                  <div className="grid grid-cols-7 gap-2">
                    <field.CheckboxInputField
                      label="Mon"
                      value="MONDAY"
                    />
                    <field.CheckboxInputField
                      label="Tue"
                      value="TUESDAY"
                    />
                    <field.CheckboxInputField
                      label="Wed"
                      value="WEDNESDAY"
                    />
                    <field.CheckboxInputField
                      label="Thu"
                      value="THURSDAY"
                    />
                    <field.CheckboxInputField
                      label="Fri"
                      value="FRIDAY"
                    />
                    <field.CheckboxInputField
                      label="Sat"
                      value="SATURDAY"
                    />
                    <field.CheckboxInputField
                      label="Sun"
                      value="SUNDAY"
                    />
                  </div>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </div>
              )}
            />
          </div>
          <DialogFooter className="col-span-2">
            <form.AppForm>
              <form.SubmitButton label="Approve Customer" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
