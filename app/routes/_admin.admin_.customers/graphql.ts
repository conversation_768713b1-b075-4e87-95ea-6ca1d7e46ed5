import { graphql } from '~/gql'

export const REGISTER_CUSTOMER = graphql(`
  mutation RegisterCustomer(
    $name: String!
    $address: String!
    $phone_number: String!
    $alternate_phone_number: String
    $plan_id: ID!
    $remarks: String
  ) {
    registerCustomer(
      name: $name
      address: $address
      phone_number: $phone_number
      alternate_phone_number: $alternate_phone_number
      plan_id: $plan_id
      remarks: $remarks
    )
  }
`)

export const ADD_CUSTOMER = graphql(`
  mutation AddCustomer(
    $name: String!
    $address: String!
    $phone_number: String!
    $alternate_phone_number: String
  ) {
    addCustomer(
      name: $name
      address: $address
      phone_number: $phone_number
      alternate_phone_number: $alternate_phone_number
    )
  }
`)

export const GET_CUSTOMERS = graphql(`
  query getCustomers(
    $first: Int!
    $page: Int
    $name: String
    $subscription_status:SubscriptionStatus
    $plan_id: ID
    $start_date: DateTime
    $end_date: DateTime
    $zone_id: ID
    $phone_number: String
    $customer_type: CustomerType
  ) {
    getCustomers(
      first: $first
      page: $page
      name: $name
      subscription_status: $subscription_status
      plan_id: $plan_id
      zone_id: $zone_id
      start_date: $start_date
      end_date: $end_date
      phone_number: $phone_number
      customer_type: $customer_type
    ) {
      data {
        id
        name
        address
        phone_number
        alternate_phone_number: alt_phone_number
        created_at
        verified_at
        subscriptionHistory {
          id
        }
        remarks
        # remarks
        # current_plan_period
        # current_plan_id
        # subscription_status
        # latestSubscription {
        #   id
        #   # status
        #   paid_at
        #   start_date
        #   remarks
        #   payable_amount
        #   plan {
        #     id
        #     name
        #     price
        #     remarks
        #   }
        # }
        # zone {
        #   id
        #   name
        # }
        # collection_day
        # currentPlan {
        #   id
        #   name
        # }
      }
    paginator_info {
      total
      current_page
      last_page
    }
  }
}
  `)
