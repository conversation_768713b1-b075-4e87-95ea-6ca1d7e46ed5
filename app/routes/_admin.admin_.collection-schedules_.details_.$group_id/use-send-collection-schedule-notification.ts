import type { SendCollectionScheduleNotificationType } from '~/schema/send-collection-schedule-notification'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { SEND_COLLECTION_SCHEDULE_NOTIFICATION } from '~/graphql/mutation/send-collection-schedule-notification'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'

export default function useSendCollectionScheduleNotification() {
  const queryClient = useQueryClient()

  const sendCollectionScheduleNotification = useMutation({
    mutationFn: async (data: SendCollectionScheduleNotificationType) => {
      const client = await graphqlClient()
      return client.request({
        document: SEND_COLLECTION_SCHEDULE_NOTIFICATION,
        variables: {
          ...data,
          week_day: data.week_day.toUpperCase(),
        },
      })
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['get-collection-sent-notification-histories'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-collection-schedule-notification-stats'],
      })
      toast.success('Collection schedule notification sent successfully')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { sendCollectionScheduleNotification }
}
