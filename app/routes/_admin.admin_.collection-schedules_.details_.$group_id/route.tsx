import { useState } from 'react'
import { useParams } from 'react-router'
import AppTooltip from '~/components/common/app-tooltip'
import BackButton from '~/components/common/back-button'
import PagePagination from '~/components/common/page-pagination'
import MessageIcon from '~/components/icons/message-icon'
import { Button } from '~/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import SendNotificationToEstablishmentDialog from './send-notification-to-establishment-dialog'
import useGetCollectionSentNotificationHistories from './use-get-collection-sent-notification-histories'

export default function CollectionScheduleDetailsByDate() {
  const { group_id } = useParams()

  const [selectedEstablishment, setSelectedEstablishment] = useState({
    id: '',
    name: '',
    phone_number: '',
  })

  const { isOpen, toggle } = useBoolean()

  const { data, isLoading, isError, page, lastPage, handlePage } = useGetCollectionSentNotificationHistories({ group_id })

  return (
    <>
      <div className="flex grow flex-col gap-4">
        <div className="flex justify-between">
          <BackButton />
        </div>
        <div className={`
          mt-4 flex w-full grow flex-col overflow-x-auto rounded-md bg-white p-2
        `}
        >
          <Table className="w-full min-w-[1000px] table-fixed caption-top">
            <TableHeader>
              <TableRow>
                <TableHead className="w-[150px]">Name</TableHead>
                <TableHead className="w-[150px]">Phone number</TableHead>
                <TableHead className="w-[150px]">Address</TableHead>
                <TableHead className="w-[150px]">Subscription Plan</TableHead>
                <TableHead className="w-[150px]">Collection Days</TableHead>
                <TableHead className="w-[150px]">Read receipt</TableHead>
                <TableHead className="w-[250px] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading
                ? (
                    <TableRow>
                      <TableCell colSpan={7} className="py-8 text-center">
                        Loading...
                      </TableCell>
                    </TableRow>
                  )
                : isError
                  ? (
                      <TableRow>
                        <TableCell
                          colSpan={7}
                          className="py-8 text-center text-red-500"
                        >
                          Error loading data
                        </TableCell>
                      </TableRow>
                    )
                  : data?.getCollectionSentNotificationHistories?.data?.length === 0
                    ? (
                        <TableRow>
                          <TableCell colSpan={7} className="py-8 text-center">
                            No records found
                          </TableCell>
                        </TableRow>
                      )
                    : (
                        data?.getCollectionSentNotificationHistories?.data?.map(record => (
                          <TableRow key={record.id}>
                            <TableCell className="truncate">
                              {record.establishment?.name || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record.establishment?.phone_number || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record.establishment?.address || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record.establishment?.plan?.name || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record.establishment?.collection_days?.map(d => d.slice(0, 3)).join(', ') || '-'}
                            </TableCell>
                            <TableCell className="truncate">
                              {record.latestWhatsApp?.message_status || '-'}
                            </TableCell>
                            <TableCell className="flex justify-end gap-x-4">
                              <AppTooltip message="Send notification">
                                <Button
                                  size="icon"
                                  variant="outline"
                                  onClick={() => {
                                    setSelectedEstablishment({
                                      id: record.establishment.id,
                                      name: record.establishment.name,
                                      phone_number: record.establishment.phone_number,
                                    })
                                    toggle(true)
                                  }}
                                >
                                  <MessageIcon />
                                </Button>
                              </AppTooltip>
                              {/* SingleNotificationDialog feature here */}
                            </TableCell>
                          </TableRow>
                        )))}

            </TableBody>
          </Table>
        </div>
        {lastPage > 1 && (
          <div className="flex justify-center">
            <PagePagination
              lastPage={lastPage}
              currentPage={page}
              handlePagePagination={handlePage}
            />
          </div>
        )}
      </div>
      {selectedEstablishment.id && (
        <SendNotificationToEstablishmentDialog
          establishmentIds={selectedEstablishment.id}
          name={selectedEstablishment.name}
          phoneNumber={selectedEstablishment.phone_number}
          isOpen={isOpen}
          toggle={toggle}
        />
      )}
    </>
  )
}
