import FormMessage from '~/components/common/form-message'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { CollectionDay } from '~/gql/graphql'
import { useAppForm } from '~/hooks/form'
import { sendCollectionScheduleNotificationSchema } from '~/schema/send-collection-schedule-notification'
import useSendCollectionScheduleNotification from './use-send-collection-schedule-notification'

interface Props {
  establishmentIds: string
  name: string
  phoneNumber: string
  isOpen: boolean
  toggle: (open: boolean) => void
}

export default function SendNotificationToEstablishmentDialog({ isOpen, toggle, establishmentIds, name, phoneNumber }: Props) {
  const { sendCollectionScheduleNotification } = useSendCollectionScheduleNotification()

  const form = useAppForm({
    defaultValues: {
      establishment_ids: [establishmentIds],
      remarks: '',
      week_day: '',
    },
    validators: {
      onSubmit: sendCollectionScheduleNotificationSchema,
    },
    onSubmit: async ({ value }) => {
      await sendCollectionScheduleNotification.mutateAsync(value, {
        onSuccess: () => {
          form.reset()
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Confirm Notification
          </DialogTitle>
          <DialogDescription>
            {name}
            {' '}
            {phoneNumber}
            {' '}
            will receive notification for collection via WhatsApp
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="flex flex-col gap-4"
        >
          <form.AppField
            name="remarks"
            children={field => <field.TextareaField label="Remarks" />}
          />

          <form.AppField
            name="week_day"
            children={field => (
              <>
                <Label className="flex flex-col items-start gap-y-2">
                  <div>Select day</div>
                  <Select
                    value={field.state.value}
                    onValueChange={(e) => {
                      field.handleChange(e)
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select day" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={CollectionDay.Monday}>Monday</SelectItem>
                      <SelectItem value={CollectionDay.Tuesday}>Tuesday</SelectItem>
                      <SelectItem value={CollectionDay.Wednesday}>Wednesday</SelectItem>
                      <SelectItem value={CollectionDay.Thursday}>Thursday</SelectItem>
                      <SelectItem value={CollectionDay.Friday}>Friday</SelectItem>
                      <SelectItem value={CollectionDay.Saturday}>Saturday</SelectItem>
                      <SelectItem value={CollectionDay.Sunday}>Sunday</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                </Label>
              </>
            )}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Confirm" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
