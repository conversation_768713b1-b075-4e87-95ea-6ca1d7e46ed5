import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_COLLECTION_SENT_NOTIFICATION_HISTORIES } from './graphql'

interface Props {
  group_id: string
}

export default function useGetCollectionSentNotificationHistories({ group_id }: Props) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePage = (page: number) => {
    setPage(page)
  }

  // const sentDate = format(new Date(date), 'yyyy-MM-dd HH:mm:ss')

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-collection-sent-notification-histories', page, group_id],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_COLLECTION_SENT_NOTIFICATION_HISTORIES,
        variables: {
          first: 15,
          page,
          group_id,
          // sent_date: sentDate,
        },
      })
    },
  })

  const lastPage = data?.getCollectionSentNotificationHistories?.paginator_info?.last_page || 1

  return { data, isLoading, isError, page, lastPage, handlePage }
}
