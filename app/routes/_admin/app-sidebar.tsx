import { LogOutIcon } from 'lucide-react'
import { NavLink, useFetcher, useLocation } from 'react-router'
import { Button } from '~/components/ui/button'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '~/components/ui/sidebar'
import useLogout from '~/hooks/use-logout'

const menu = [
  {
    title: 'Payment Records',
    url: '/admin/payment-records',
  },
  {
    title: 'Customers',
    url: '/admin/customers',
  },
  {
    title: 'Plans',
    url: '/admin/plans',
  },
  {
    title: 'Inventory',
    url: '/admin/inventory',
  },
  {
    title: 'Collection Schedules',
    url: '/admin/collection-schedules',
  },
  {
    title: 'Reports',
    url: '/admin/reports',
  },
  {
    title: 'Subscription Requests',
    url: '/admin/subscription-requests',
  },
]

export default function AppSidebar() {
  const fetcher = useFetcher()
  const location = useLocation()
  const { logout } = useLogout()

  const handleLogout = () => {
    logout.mutate(undefined, {
      onError: () => {
        void fetcher.submit(null, {
          action: '/logout',
          method: 'POST',
        })
      },
      onSuccess: () => {
        void fetcher.submit(null, {
          action: '/logout',
          method: 'POST',
        })
      },
    })
  }

  return (
    <Sidebar>
      <SidebarContent className="flex flex-col">
        <SidebarGroup className="grow">
          <SidebarGroupContent>
            <SidebarMenu>
              {menu.map(item => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={location.pathname.startsWith(item.url)}
                  >
                    <NavLink to={item.url}>{item.title}</NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarMenu>
            <SidebarMenuItem>
              <Button
                onClick={handleLogout}
                isLoading={logout.isPending}
                variant="destructive"
                className="w-full"
              >
                <LogOutIcon className="mr-1" />
                Log Out
              </Button>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
