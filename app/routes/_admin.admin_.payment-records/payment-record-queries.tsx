import type { Plan } from '~/gql/graphql'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { months } from '~/lib/constants'
import { generateYearOptions } from '~/lib/generate-year-options'
import useGetPaymentRecords from './use-get-payment-records'

interface Props {
  plans: Plan[]
}

export default function PaymentRecordQueries({ plans }: Props) {
  const { filters, handleSearch } = useGetPaymentRecords()

  const yearOptions = generateYearOptions()
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleSearch({ [e.target.name]: e.target.value })
  }

  return (
    <div className="flex gap-4">
      <Label className="flex basis-1/3 flex-col items-start gap-y-2">
        <div>Search</div>
        <Input className="w-full" placeholder="Search" value={filters.keyword} name="name" onChange={handleInputChange} />
      </Label>
      <div className="flex basis-2/3 gap-4">
        <Label className="flex w-full flex-col items-start gap-y-2">
          <div>Payment Status</div>
          <Select
            value={filters.isPaid}
            onValueChange={value => handleSearch({ isPaid: value })}
          >
            <SelectTrigger className="w-full bg-white">
              <SelectValue placeholder="Select payment status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="true">Paid</SelectItem>
              <SelectItem value="false">Unpaid</SelectItem>
            </SelectContent>
          </Select>
        </Label>
        <Label className="flex w-full flex-col items-start gap-y-2">
          <div>Subscription Plan</div>
          <Select value={filters.planId} onValueChange={value => handleSearch({ planId: value === 'All' ? '' : value })}>
            <SelectTrigger className="w-full bg-white">
              <SelectValue placeholder="Select plan" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {plans.map((plan) => {
                return <SelectItem key={plan.id} value={plan.id}>{plan.name}</SelectItem>
              })}
            </SelectContent>
          </Select>
        </Label>
        <Label className="flex w-full flex-col items-start gap-y-2">
          <div>Month</div>
          <Select
            value={filters.month ? filters.month.toString() : 'all'}
            onValueChange={value => handleSearch({ month: value === 'all' ? undefined : Number.parseInt(value) })}
          >
            <SelectTrigger className="w-full bg-white">
              <SelectValue placeholder="Select month" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {months.map(monthOption => (
                <SelectItem key={monthOption.value} value={monthOption.value.toString()}>
                  {monthOption.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </Label>
        <Label className="flex w-full flex-col items-start gap-y-2">
          <div>Year</div>
          <Select
            value={filters.year ? filters.year.toString() : 'all'}
            onValueChange={value => handleSearch({ year: value === 'all' ? undefined : Number.parseInt(value) })}
          >
            <SelectTrigger className="w-full bg-white">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              {yearOptions.map(yearOption => (
                <SelectItem key={yearOption} value={yearOption.toString()}>
                  {yearOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </Label>
      </div>
    </div>
  )
}
