import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, parseAsString, useQueryState } from 'nuqs'
import { useDebounce } from 'use-debounce'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_PAYMENT_RECORDS } from './graphql'

export default function useGetPaymentRecords() {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))
  const [planId, setPlanId] = useQueryState('zone_id', parseAsString.withDefault(''))
  const [keyword, setKeyword] = useQueryState('keyword', parseAsString.withDefault(''))
  const [month, setMonth] = useQueryState('month', parseAsInteger)
  const [year, setYear] = useQueryState('year', parseAsInteger)
  const [isPaid, setIsPaid] = useQueryState('is_paid', parseAsString.withDefault('all'))

  const handlePage = (page: number) => {
    setPage(page)
  }

  const handleSearch = (params: {
    planId?: string
    keyword?: string
    month?: number
    year?: number
    isPaid?: string
  }) => {
    if (params.planId !== undefined)
      setPlanId(params.planId)
    if (params.keyword !== undefined)
      setKeyword(params.keyword)
    if (params.month !== undefined)
      setMonth(params.month)
    if (params.year !== undefined)
      setYear(params.year)
    if (params.isPaid !== undefined)
      setIsPaid(params.isPaid)
    setPage(1)
  }

  const [debouncedKeyword] = useDebounce(keyword, 500)

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-payment-records', page, planId, debouncedKeyword, month, year, isPaid],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_PAYMENT_RECORDS,
        variables: {
          first: 15,
          page,
          keyword: debouncedKeyword || undefined,
          plan_id: planId || undefined,
          month: month || undefined,
          year: year || undefined,
          is_paid: isPaid === 'all' ? undefined : isPaid === 'true',
        },
      })
    },
  })

  const lastPage = data?.getPaymentRecords?.paginator_info?.last_page || 1

  return { data, isLoading, isError, handlePage, handleSearch, page, lastPage, filters: { planId, keyword, month, year, isPaid } }
}
