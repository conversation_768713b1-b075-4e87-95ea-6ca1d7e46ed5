import { graphql } from '~/gql'

export const GET_PAYMENT_RECORDS = graphql(`
  query GetPaymentRecords(
    $first: Int!
    $month: Int
    $year: Int
    $page: Int
    $keyword: String
    $plan_id: ID
    $is_paid: Boolean
  ) {
    getPaymentRecords(
      first: $first
      page: $page
      month: $month
      year: $year
      keyword: $keyword
      plan_id: $plan_id
      is_paid: $is_paid
    ) {
      data {
        id
        # overdue
        start_date
        end_date
        payable_amount
        customer {
          id
          name
          phone_number
          address
        }
        subscriptionItems {
          id
          plan {
            id
            name
          }
          establishment {
            id
            name
            address
            phone_number
            zone {
              id
              name
            }
          }
        }
        paid_at
        payment_mode
        latestWhatsApp {
          id
          message_status
        }
      }
      paginator_info {
        total
        current_page
        last_page
      }
    }
  }
`)
