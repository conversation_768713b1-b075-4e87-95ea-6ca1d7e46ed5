import type { GenerateReceiptLinkType } from '~/schema/generate-receipt-link'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '~/components/ui/dialog'
import { useAppForm } from '~/hooks/form'
import useGenerateReceiptLink from '~/hooks/use-generate-receipt-link'
import { generateReceiptLinkSchema } from '~/schema/generate-receipt-link'

interface Props {
  isOpen: boolean
  toggle: (open: boolean) => void
  id: string
  phoneNumber: string
}

export default function SendReceiptLinkDialog({ isOpen, toggle, id, phoneNumber }: Props) {
  const { generateReceiptLink } = useGenerateReceiptLink()

  const form = useAppForm({
    defaultValues: {
      id,
      phone_number: phoneNumber,
      should_send: false,
    } as GenerateReceiptLinkType,
    validators: {
      onSubmit: generateReceiptLinkSchema,
    },
    onSubmit: async ({ value }) => {
      await generateReceiptLink.mutateAsync(value, {
        onSuccess: (_data) => {
          // todo something with the returned data
          toggle(false)
        },
      })
    },
  })

  return (
    <Dialog open={isOpen} onOpenChange={toggle}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            Send Receipt Link
          </DialogTitle>
          <DialogDescription>
            Recipient will receive payment receipt via Whatsapp
          </DialogDescription>
        </DialogHeader>
        <form
          onSubmit={(e) => {
            e.preventDefault()
            e.stopPropagation()
            form.handleSubmit()
          }}
          className="space-y-4"
        >
          <form.AppField
            name="phone_number"
            children={field => <field.MobileInputField label="Phone Number" />}
          />
          <form.AppField
            name="should_send"
            children={field => <field.CheckboxField label="Should send?" />}
          />
          <DialogFooter>
            <form.AppForm>
              <form.SubmitButton label="Send Receipt Link" />
            </form.AppForm>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
